﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Result
{
    public class IResultContainer
    {
        private string _ContainerNo;
        private string _UnloadingTime;
        private string _ContainerPickupTime;
        private string _ReturnTime;

        [System.ComponentModel.DisplayName("卸货时间")]
        public string UnloadingTime { get => _UnloadingTime; set => _UnloadingTime = value; }
        [System.ComponentModel.DisplayName("提箱时间")]
        public string ContainerPickupTime { get => _ContainerPickupTime; set => _ContainerPickupTime = value; }
        [System.ComponentModel.DisplayName("还箱时间")]
        public string ReturnTime { get => _ReturnTime; set => _ReturnTime = value; }
        [System.ComponentModel.DisplayName("集装箱号")]
        public string ContainerNo { get => _ContainerNo; set => _ContainerNo = value; }


        public IResultContainer SetNewContainerItem(string containerNo, string pickupDate, string unloadingDate, string returnDate)
        {
            try
            {
                if (string.IsNullOrEmpty(containerNo))
                    throw new Exception("集装箱号不可为空");


                this._ContainerNo = containerNo;

                DateTime dat;

                if (DateTime.TryParse(pickupDate, out dat))
                {
                    this._ContainerPickupTime = dat.ToString("yyyy-MM-dd");
                }
                if (DateTime.TryParse(unloadingDate, out dat))
                {
                    this._UnloadingTime = dat.ToString("yyyy-MM-dd");
                }
                if (DateTime.TryParse(returnDate, out dat))
                {
                    this._ReturnTime = dat.ToString("yyyy-MM-dd");
                }
                return this;

            }
            catch (Exception exc)
            {
                throw;
            }
        }

    }
}
