﻿using HIH.Framework.AutoCrawingManager.Result;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager
{
    public class IProcessItem
    {

        public IProcessItem(int serial,string processName, IProcessType type)
        {
            this.SerialNumber = serial;
            this.ProcessItemName = processName;
            this.PType = type;
        }

        public IProcessItem(int serial, string processName, IProcessType type, Func<string, IResult> interpreter)
        {
            this.SerialNumber = serial;
            this.ProcessItemName = processName;
            this.PType = type;
            this.Interpreter = interpreter;
        }


        public string ProcessItemName { get; set; }
        public int SerialNumber { get; set; }
        public IProcessType PType { get; set; }
        public string JScript { get; set; }
        public Func<string, IResult> Interpreter { get; } // 统一使用 object 作为返回值类型

    }
}
