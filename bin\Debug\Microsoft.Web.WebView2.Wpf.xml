<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Web.WebView2.Wpf</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties">
            <summary>
            This class is a bundle of the most common parameters used to create <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> and <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> instances.
            Its main purpose is to be set to <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CreationProperties"/> in order to customize the environment and/or controller used by a <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> during implicit initialization.
            It is also a nice WPF integration utility which allows commonly used environment/controller parameters to be dependency properties and be created and used in markup.
            </summary>
            <remarks>
            This class isn't intended to contain all possible environment or controller customization options.
            If you need complete control over the environment and/or controller used by a WebView2 control then you'll need to initialize the control explicitly by
            creating your own environment (with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/>) and/or controller options (with <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateCoreWebView2ControllerOptions"/>) and passing them to <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>
            *before* you set the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.Source"/> property to anything.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties"/> with default data for all properties.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.BrowserExecutableFolderProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.BrowserExecutableFolder"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.BrowserExecutableFolder">
            <summary>
            Gets or sets the value to pass as the browserExecutableFolder parameter of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.UserDataFolderProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.UserDataFolder"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.UserDataFolder">
            <summary>
            Gets or sets the value to pass as the userDataFolder parameter of <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.LanguageProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.Language"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.Language">
            <summary>
            Gets or sets the value to use for the Language property of the CoreWebView2EnvironmentOptions parameter passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AdditionalBrowserArgumentsProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AdditionalBrowserArguments"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AdditionalBrowserArguments">
            <summary>
            Gets or sets the value to use for the AdditionalBrowserArguments property of the CoreWebView2EnvironmentOptions parameter passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AreBrowserExtensionsEnabledProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AreBrowserExtensionsEnabled"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.AreBrowserExtensionsEnabled">
            <summary>
            Gets or sets the value to use for the AreBrowserExtensionsEnabled property of the CoreWebView2EnvironmentOptions parameter passed to <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2Environment.CreateAsync(System.String,System.String,Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions)"/> when creating an environment with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ProfileNameProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ProfileName"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ProfileName">
            <summary>
            Gets or sets the value to use for the ProfileName property of the CoreWebView2ControllerOptions parameter passed to CreateCoreWebView2ControllerWithOptionsAsync when creating an controller with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.IsInPrivateModeEnabledProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.IsInPrivateModeEnabled"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.IsInPrivateModeEnabled">
            <summary>
            Gets or sets the value to use for the IsInPrivateModeEnabled property of the CoreWebView2ControllerOptions parameter passed to CreateCoreWebView2ControllerWithOptionsAsync when creating an controller with this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ScriptLocaleProperty">
            <summary>
            The WPF DependencyProperty which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ScriptLocale"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.ScriptLocale">
            <summary>
            Gets or sets the value to use for the ScriptLocale property of the CoreWebView2ControllerOptions parameter passed to CreateCoreWebView2ControllerWithOptionsAsync when creating an controller with this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.CreateEnvironmentAsync">
            <summary>
            Create a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> using the current values of this instance's properties.
            </summary>
            <returns>A task which will provide the created environment on completion, or null if no environment-related options are set.</returns>
            <remarks>
            As long as no other properties on this instance are changed, repeated calls to this method will return the same task/environment as earlier calls.
            If some other property is changed then the next call to this method will return a different task/environment.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.CoreWebView2CreationProperties.CreateCoreWebView2ControllerOptions(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <summary>
            Create a <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> using the current values of this instance's properties.
            </summary>
            <returns>A <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> object or null if no controller-related properties are set.</returns>
            <exception cref="T:System.NullReferenceException">Thrown if the parameter environment is null.</exception>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.Direct3DHelper">
            <summary>
            This class provides helper methods for working with Direct3D in the WebView2 WPF control.
            It includes methods for creating Direct3D9 and Direct3D11 devices, creating textures,
            copying resources, and getting shared handles, among others. These methods are used in
            the GraphicsItemD3DImage class to interact with Direct3D objects and resources.
            </summary>
            <remarks>
            The class includes PInvoke declarations for calling Direct3D functions from unmanaged code.
            It also defines several COM interfaces and structures necessary for interacting with
            Direct3D objects.
            Note: This class is intended for internal use by the WebView2 WPF control and should not
            be used directly by application developers.
            </remarks>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.GraphicsItemD3DImage">
            <summary>
            This Class is a <see cref="T:System.Windows.Interop.D3DImage"/> capturing the content of the provided <see cref="T:Windows.UI.Composition.ContainerVisual"/> (the WebView2 visual).
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.ImplicitInitGate">
            <summary>
            Tracks the conditions which block implicit initialization and whether it has been requested or not.
            The analogy is a set of gates which are either open (implicit init allowed) or closed (will have to wait).
            All sub-gates must be open before implicit init can proceed.
            If implicit init is requested while the gate is open then it happens immediately.
            If it's requested while the gate is closed then it occurs when the gate becomes open.
            </summary>
            <remarks>
            It should be reasonably straight-forward to expand this class in the future to:
            * add new sub-gates to further restrict when implicit initialization can occur
            * support storing and invoking multiple actions next time the gate is open instead of only one
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.ISupportInitializeOpen">
            <summary>
            Tracks whether a sub-gate regarding <see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.BeginInit"/>/<see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.EndInit"/> is open or closed.
            This sub-gate is only closed after calls to `BeginInit` and before an equal number of calls to `EndInit`.
            </summary>
            <remarks>
            We don't want implicit initialization to occur in between those calls,
            because implicit initialization is a side effect of setting the Source property,
            and side effects of setting properties during that period are supposed to be delayed until `EndInit`.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.ISupportInitializeCount">
            <summary>
            How many times <see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.BeginInit"/> has been called without <see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.EndInit"/> being called.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.SyncContextOpen">
            <summary>
            Tracks whether a sub-gate regarding <see cref="P:System.Threading.SynchronizationContext.Current"/> is open or closed.
            This sub-gate is closed if `SynchronizationContext.Current == null`.
            </summary>
            <remarks>
            Initialization won't work without a `SynchronizationContext` because otherwise an `await` might resume on a different thread.
            As far as I know so far this only occurs before an event loop as started on the running thread.
            Once there's an event loop running the `SynchronizationContext` ensures that `await`s resume in the same event loop (i.e. same thread).
            Although it's a rare corner case, it's possible to create a `Window` w/ `WebView2` before an app's event loop starts.
            This sub-gate handles that corner case.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.PendingInitAction">
            <summary>
            An action which will trigger initialization next time the gate is open (and only once).
            </summary>
            <remarks>
            This basically tracks whether or not implicit initialization has been requested while the gate is closed.
            If this is non-null then it should be a delegate that calls <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.BeginInit">
            <summary>
            Closes the gate until <see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.EndInit"/> is called an equal number of times.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.EndInit">
            <summary>
            Opens the gate closed by <see cref="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.BeginInit"/> after being called the same number of times.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.OnSynchronizationContextExists(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            A handler that should be attached to an event which indicates that <see cref="P:System.Threading.SynchronizationContext.Current"/> exists.
            The best one I know of right now is <see cref="E:System.Windows.FrameworkElement.Loaded"/>.
            When the handler is called, the gate will re-evaluate its state and potentially allow any pending initialization action.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.RunWhenOpen(System.Action)">
            <summary>
            Run a given action when the gate is open.
            </summary>
            <remarks>
            If the gate is currently open then the action runs immediately.
            Otherwise the action runs next time the gate is discovered to be open.
            The action is only ever run once; it will not run again a second/subsequent time the gate opens.
            If the gate is closed and another action is already pending then the new action *overwrites* the current one (i.e. the currently stored action will never run).
            To "forget" a currently stored action, pass `null`.
            </remarks>
            <param name="initAction">Action to run when the gate is open, or null to clear a previously specified action.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.ImplicitInitGate.OnDataChanged">
            <summary>
            Examine our overall open/closed state and run any pending action if appropriate.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.IWebView2">
            <summary>
            The public interfaces of WebView2 WPF control.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties">
            <summary>
            Gets or sets a bag of options which are used during initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/>.
            Setting this property will not work after initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> has started (the old value will be retained).
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2">
            <summary>
            Accesses the complete functionality of the underlying <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> COM API.
            Returns <c>null</c> until initialization has completed.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.SourceChanged">
            <summary>
            A wrapper around the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>.
            The only difference between this event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> is the first parameter that's passed to handlers.
            Handlers of this event will receive the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whereas handlers of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/> will receive the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> instance.
            </summary>
            <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.SourceChanged"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.NavigationStarting">
            <summary>
            A wrapper around the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>.
            The only difference between this event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> is the first parameter that's passed to handlers.
            Handlers of this event will receive the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whereas handlers of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/> will receive the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> instance.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationStarting"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.NavigationCompleted">
            <summary>
            A wrapper around the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>.
            The only difference between this event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> is the first parameter that's passed to handlers.
            Handlers of this event will receive the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whereas handlers of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/> will receive the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> instance.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.NavigationCompleted"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.ZoomFactorChanged">
            <summary>
            The event is raised when the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.ZoomFactor"/> property changes.
            This event directly exposes <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.ZoomFactor"/>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactorChanged"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.ContentLoading">
            <summary>
            A wrapper around the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>.
            The only difference between this event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> is the first parameter that's passed to handlers.
            Handlers of this event will receive the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whereas handlers of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/> will receive the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> instance.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.ContentLoading"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.WebMessageReceived">
            <summary>
            A wrapper around the <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>.
            The only difference between this event and <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/> is the first parameter that's passed to handlers.
            Handlers of this event will receive the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whereas handlers of <see cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/> will receive the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> instance.
            </summary>
            <seealso cref="E:Microsoft.Web.WebView2.Core.CoreWebView2.WebMessageReceived"/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2InitializationCompleted">
            <summary>
            This event is triggered either
            1) when the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> has finished being initialized (regardless of how initialization was triggered) but before it is used for anything, or
            2) if the initialization failed.
            You should handle this event if you need to perform one time setup operations on the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> which you want to affect all of its usages.
            (e.g. adding event handlers, configuring settings, installing document creation scripts, adding host objects).
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
            <remarks>
            This sender will be the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, whose <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> property will now be valid (i.e. non-null) for the first time
            if <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs.IsSuccess"/> is <c>true</c>.
            Unlikely this event can fire second time (after reporting initialization success first)
            if the initialization is followed by navigation which fails.
            </remarks>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            Explicitly triggers initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/>.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
            <param name="environment">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> that should be used to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            Creating your own environment gives you control over several options that affect how the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> is initialized.
            If you pass an environment to this method then it will override any settings specified on the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> property.
            If you pass <c>null</c> (the default value) and no value has been set to <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> then a default environment will be created and used automatically.
            </param>
            <param name="controllerOptions">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions"/> that should be used to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            Creating your own controller options gives you control over several options that affect how the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> is initialized.
            If you pass a controllerOptions to this method then it will override any settings specified on the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> property.
            If you pass <c>null</c> (the default value) and no value has been set to <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> then a default controllerOptions will be created and used automatically.
            </param>
            <returns>
            A Task that represents the background initialization process.
            When the task completes then the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> property will be available for use (i.e. non-null).
            Note that the control's <see cref="E:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2InitializationCompleted"/> event will be invoked before the task completes.
            </returns>
            <remarks>
            Unless previous initialization has already failed, calling this method additional times with the same parameter will have no effect (any specified environment is ignored) and return the same Task as the first call.
            Unless previous initialization has already failed, calling this method after initialization has been implicitly triggered by setting the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.Source"/> property will have no effect if no environment is given
            and simply return a Task representing that initialization already in progress, unless previous initialization has already failed.
            Unless previous initialization has already failed, calling this method with a different environment after initialization has begun will result in an <see cref="T:System.ArgumentException"/>. For example, this can happen if you begin initialization
            by setting the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.Source"/> property and then call this method with a new environment, if you begin initialization with <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> and then call this method with a new
            environment, or if you begin initialization with one environment and then call this method with no environment specified.
            When this method is called after previous initialization has failed, it will trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> again.
            Note that even though this method is asynchronous and returns a Task, it still must be called on the UI thread like most public functionality of most UI controls.
            <para>
            The following summarizes the possible error values and a description of why these errors occur.
            <list type="table">
            <listheader>
            <description>Error Value</description>
            <description>Description</description>
            </listheader>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_NOT_SUPPORTED)</c></description>
            <description>*\\Edge\\Application* path used in browserExecutableFolder.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_INVALID_STATE)</c></description>
            <description>Specified options do not match the options of the WebViews that are currently running in the shared browser process.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_INVALID_WINDOW_HANDLE)</c></description>
            <description>WebView2 Initialization failed due to an invalid host HWND parentWindow.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_DISK_FULL)</c></description>
            <description>WebView2 Initialization failed due to reaching the maximum number of installed runtime versions.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_PRODUCT_UNINSTALLED</c></description>
            <description>If the Webview depends upon an installed WebView2 Runtime version and it is uninstalled.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND)</c></description>
            <description>Could not find Edge installation.</description>
            </item>
            <item>
            <description><c>HRESULT_FROM_WIN32(ERROR_FILE_EXISTS)</c></description>
            <description>User data folder cannot be created because a file with the same name already exists.</description>
            </item>
            <item>
            <description><c>E_ACCESSDENIED</c></description>
            <description>Unable to create user data folder, Access Denied.</description>
            </item>
            <item>
            <description><c>E_FAIL</c></description>
            <description>Edge runtime unable to start.</description>
            </item>
            </list>
            </para>
            </remarks>
            <exception cref="T:System.ArgumentException">
            Thrown if this method is called with a different environment than when it was initialized. See Remarks for more info.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if <see cref="P:System.Threading.SynchronizationContext.Current"/> is null, which probably indicates that the application's event loop hasn't started yet.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <summary>
            Explicitly triggers initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/>.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
            <param name="environment">
            A pre-created <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> that should be used to create the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            Creating your own environment gives you control over several options that affect how the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> is initialized.
            If you pass an environment to this method then it will override any settings specified on the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> property.
            If you pass <c>null</c> and no value has been set to <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> then a default environment will be created and used automatically.
            </param>
            <returns>
            A Task that represents the background initialization process.
            When the task completes then the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> property will be available for use (i.e. non-null).
            Note that the control's <see cref="E:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2InitializationCompleted"/> event will be invoked before the task completes.
            </returns>
            <remarks>
            Unless previous initialization has already failed, calling this method additional times with the same parameter will have no effect (any specified environment is ignored) and return the same Task as the first call.
            Unless previous initialization has already failed, calling this method after initialization has been implicitly triggered by setting the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.Source"/> property will have no effect if no environment is given
            and simply return a Task representing that initialization already in progress, unless previous initialization has already failed.
            Unless previous initialization has already failed, calling this method with a different environment after initialization has begun will result in an <see cref="T:System.ArgumentException"/>. For example, this can happen if you begin initialization
            by setting the <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.Source"/> property and then call this method with a new environment, if you begin initialization with <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> and then call this method with a new
            environment, or if you begin initialization with one environment and then call this method with no environment specified.
            When this method is called after previous initialization has failed, it will trigger initialization of the control's <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> again.
            Note that even though this method is asynchronous and returns a Task, it still must be called on the UI thread like most public functionality of most UI controls.
            </remarks>
            <exception cref="T:System.ArgumentException">
            Thrown if this method is called with a different environment than when it was initialized. See Remarks for more info.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if <see cref="P:System.Threading.SynchronizationContext.Current"/> is null, which probably indicates that the application's event loop hasn't started yet.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.BeginInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Prevents the control from implicitly initializing its <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> until <see cref="M:Microsoft.Web.WebView2.Wpf.IWebView2.EndInit"/> is called.
            Does *not* prevent explicit initialization of the CoreWebView2 (i.e. <see cref="M:Microsoft.Web.WebView2.Wpf.IWebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>).
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            Note that the "Initialize" in ISupportInitialize and the "Init" in BeginInit/EndInit mean
            something different and more general than this control's specific concept of initializing
            its CoreWebView2 (explicitly or implicitly).  This ISupportInitialize pattern is a general
            way to set batches of properties on the control to their initial values without triggering
            any dependent side effects until all of the values are set (i.e. until EndInit is called).
            In the case of this control, a specific side effect to be avoided is triggering implicit
            initialization of the CoreWebView2 when setting the Source property.
            For example, normally if you set <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CreationProperties"/> after you've already set Source,
            the data set to CreationProperties is ignored because implicit initialization has already started.
            However, if you set the two properties (in the same order) in between calls to BeginInit and
            EndInit then the implicit initialization of the CoreWebView2 is delayed until EndInit, so the data
            set to CreationProperties is still used even though it was set after Source.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.EndInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Invokes any functionality that has been delayed since the corresponding call to <see cref="M:Microsoft.Web.WebView2.Wpf.IWebView2.BeginInit"/>.
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            See the documentation of <see cref="M:Microsoft.Web.WebView2.Wpf.IWebView2.BeginInit"/> for more information.
            </remarks>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.Source">
            <summary>
            The top-level <see cref="T:System.Uri"/> which the WebView is currently displaying (or will display once initialization of its <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> is finished).
            Generally speaking, getting this property is equivalent to getting the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.Source"/> property and setting this property (to a different value) is equivalent to calling the <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Navigate(System.String)"/> method.
            </summary>
            <remarks>
            Getting this property before the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> has been initialized will retrieve the last Uri which was set to it, or null (the default) if none has been.
            Setting this property before the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> has been initialized will cause initialization to start in the background (if not already in progress), after which the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> will navigate to the specified <see cref="T:System.Uri"/>.
            This property can never be set back to null or to a relative <see cref="T:System.Uri"/>.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </remarks>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <exception cref="T:System.NotImplementedException">Thrown if the property is set to <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException">Thrown if the property is set to a relative <see cref="T:System.Uri"/> (i.e. a <see cref="T:System.Uri"/> whose <see cref="P:System.Uri.IsAbsoluteUri"/> property is <c>false</c>).</exception>
            <seealso cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.CanGoBack">
            <summary>
            Returns <c>true</c> if the WebView can navigate to a previous page in the navigation history.
            Wrapper around the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/> property of <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/>.
            If <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> isn't initialized yet then returns <c>false</c>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.CanGoForward">
            <summary>
            Returns <c>true</c> if the WebView can navigate to a next page in the navigation history.
            Wrapper around the <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/> property of <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/>.
            If <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> isn't initialized yet then returns <c>false</c>.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoForward"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.ZoomFactor">
            <summary>
            The zoom factor for the WebView.
            This property directly exposes <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/>, see its documentation for more info.
            Getting this property before the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> has been initialized will retrieve the last value which was set to it, or <c>1.0</c> (the default) if none has been.
            The most recent value set to this property before the CoreWebView2 has been initialized will be set on it after initialization.
            </summary>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.ZoomFactor"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.DefaultBackgroundColor">
            <summary>
            The default background color for the WebView.
            This property directly exposes <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.DefaultBackgroundColor"/>, see its documentation for more info.
            Getting this property before the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> has been initialized will retrieve the last value which was
            set to it, or <c>Color.White</c> (the default) if none has been.
            The most recent value set to this property before CoreWebView2Controller has been initialized will be set on it after initialization.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.DesignModeForegroundColor">
            <summary>
            The foreground color to be used in design mode.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.IWebView2.AllowExternalDrop">
            <summary>
            The AllowExternalDrop property for the WebView.
            This property directly exposes <see cref="P:Microsoft.Web.WebView2.Core.CoreWebView2Controller.AllowExternalDrop"/>, see its documentation for more info.
            Getting this property before the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Controller"/> has been initialized will retrieve the last value which was
            set to it, or <c>true</c> (the default) if none has been.
            The most recent value set to this property before CoreWebView2Controller has been initialized will be set on it after initialization.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.GoBack">
            <summary>
            Navigates the WebView to the previous page in the navigation history.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoBack"/>
            If <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> hasn't been initialized yet then does nothing.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="P:Microsoft.Web.WebView2.Core.CoreWebView2.CanGoBack"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.GoForward">
            <summary>
            Navigates the WebView to the next page in the navigation history.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward"/>.
            If <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/> hasn't been initialized yet then does nothing.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.GoForward"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.Reload">
            <summary>
            Reloads the current page.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> hasn't been initialized yet, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Reload"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.Stop">
            <summary>
            Stops all navigations and pending resource fetches.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Stop"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> hasn't been initialized yet, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.Stop"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.NavigateToString(System.String)">
            <summary>
            Initiates a navigation to htmlContent as source HTML of a new document.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> hasn't been initialized yet, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <remarks>The <c>htmlContent</c> parameter may not be larger than 2 MB (2 * 1024 * 1024 bytes) in total size. The origin of the new page is <c>about:blank</c>.</remarks>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.NavigateToString(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.ExecuteScriptAsync(System.String)">
            <summary>
            Executes JavaScript code from the javaScript parameter in the current top level document rendered in the WebView.
            Equivalent to calling <see cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            Thrown if <see cref="P:Microsoft.Web.WebView2.Wpf.IWebView2.CoreWebView2"/> hasn't been initialized yet, or if the calling thread isn't the thread which created this object (usually the UI thread). See <see cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/> for more info.
            May also be thrown if the browser process has crashed unexpectedly and left the control in an invalid state. We are considering throwing a different type of exception for this case in the future.
            </exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if <see cref="M:System.IDisposable.Dispose"/> has already been called on the control.</exception>
            <seealso cref="M:System.Windows.Threading.DispatcherObject.VerifyAccess"/>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2.ExecuteScriptAsync(System.String)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2.Focus">
            <summary>
            Attempts to set focus to the WebView2 Control.
            Equivalent to calling <see cref="M:System.Windows.UIElement.Focus"/>.
            Returns <c>true</c> if keyboard focus and logical focus were set to this element;
            <c>false</c> if only logical focus was set to this element, or if the call to this method did not force the focus to change.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.IWebView2Private">
            <summary>
            Defines internal operations specific to the WebView2 WPF control, separating the control-specific logic
            that cannot be shared across different types of controls.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2Private.InitializeController(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <summary>
            Initializes the WebView2 controller with operations specific to the control type.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.IWebView2Private.UninitializeController">
            <summary>
            Unregister controller's handlers specific to the control type.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.WebView2">
             <summary>
             A control to embed web content in a WPF application.
             </summary>
             <remarks>
             This control is effectively a wrapper around the [WebView2 COM API](https://aka.ms/webview2).
             You can directly access the underlying
             ICoreWebView2 interface and all of its functionality by accessing the
             <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> property. Some of the most common COM
             functionality is also accessible directly through wrapper
             methods/properties/events on the control.
            
             Upon creation, the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> property will be
             <c>null</c>. This is because creating the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> is an
             expensive operation which involves things like launching Edge browser
             processes. There are two ways to cause the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> to
             be created:
             <list type="bullet">
             <item><description>
             Call the <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> method.  This is
             referred to as explicit initialization.
             </description></item>
             <item><description>
             Set the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.Source"/> property (which could be done from
             markup, for example).  This is referred to as implicit initialization.
             Either option will start initialization in the background and return
             back to the caller without waiting for it to finish.
             To specify options regarding the initialization process, either pass
             your own <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> to <see
             cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> or set the control's <see
             cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CreationProperties"/> property prior to initialization.
             </description></item>
             </list>
            
             When initialization has finished (regardless of how it was triggered or
             whether it succeeded) then the following things will occur, in this
             order:
             <list type="number">
             <item><description>
             The control's <see cref="E:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2InitializationCompleted"/> event
             will be invoked. If you need to perform one time setup operations on
             the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> prior to its use then you should
             do so in a handler for that event.
             </description></item>
             <item><description>
             If initialization was successful and a Uri has been set to the <see
             cref="P:Microsoft.Web.WebView2.Wpf.WebView2.Source"/> property then the control will start navigating to it in
             the background (i.e. these steps will continue without waiting for the
             navigation to finish).
             </description></item>
             <item><description>
             The Task returned from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> will
             complete.
             </description></item>
             </list>
            
             For more details about any of the methods/properties/events involved in
             the initialization process, see its specific documentation.
            
             Because the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> is a very heavyweight
             object (potentially responsible for multiple running processes and
             megabytes of disk space) the control implements <see
             cref="T:System.IDisposable"/> to provide an explicit means to free it.
             Calling <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.Dispose(System.Boolean)"/> will release the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/>
             and its underlying resources (except any that are also being used by other
             WebViews), and reset <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> to <c>null</c>. After <see
             cref="M:Microsoft.Web.WebView2.Wpf.WebView2.Dispose(System.Boolean)"/> has been called the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> cannot be
             re-initialized, and any attempt to use functionality which requires it
             will throw an <see cref="T:System.ObjectDisposedException"/>.
            
             Accelerator key presses (e.g. Ctrl+P) that occur within the control will
             fire standard key press events such as OnKeyDown. You can suppress the
             control's default implementation of an accelerator key press (e.g.
             printing, in the case of Ctrl+P) by setting the Handled property of its
             EventArgs to true. Also note that the underlying browser process is
             blocked while these handlers execute, so:
             <list type="number">
             <item>
             <description>You should avoid doing a lot of work in these handlers.</description>
             </item>
             <item><description>
             Some of the WebView2 and CoreWebView2 APIs may throw errors if
             invoked within these handlers due to being unable to communicate with
             the browser process.
             </description></item>
             </list>
             If you need to do a lot of work and/or invoke WebView2 APIs in response to
             accelerator keys then consider kicking off a background task or queuing
             the work for later execution on the UI thread.
            
             Note that this control extends <see cref="T:System.Windows.Interop.HwndHost"/> in order to embed
             windows which live outside of the WPF ecosystem. This has some
             implications regarding the control's input and output behavior as well as
             the functionality it "inherits" from <see cref="T:System.Windows.UIElement"/> and <see
             cref="T:System.Windows.FrameworkElement"/>.
             See the <see cref="T:System.Windows.Interop.HwndHost"/> and [WPF/Win32 interop](/dotnet/framework/wpf/advanced/wpf-and-win32-interoperation#hwnds-inside-wpf)
             documentation for more information.
             </remarks>
             <seealso cref="T:System.Windows.Interop.HwndHost"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.#ctor">
            <summary>
            Creates a new instance of a WebView2 control.
            Note that the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> will be null until initialized.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> class documentation for an initialization overview.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.CreationPropertiesProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CreationProperties"/> property.
            </summary>
            <seealso cref="T:System.Windows.DependencyProperty"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.CreationProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.BuildWindowCore(System.Runtime.InteropServices.HandleRef)">
            <summary>
            This is overridden from <see cref="T:System.Windows.Interop.HwndHost"/> and is called to instruct us to create our HWND.
            </summary>
            <param name="hwndParent">The HWND that we should use as the parent of the one we create.</param>
            <returns>The HWND that we created.</returns>
            <seealso cref="M:System.Windows.Interop.HwndHost.BuildWindowCore(System.Runtime.InteropServices.HandleRef)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.DestroyWindowCore(System.Runtime.InteropServices.HandleRef)">
            <summary>
            This is overridden from <see cref="T:System.Windows.Interop.HwndHost"/> and is called to instruct us to destroy our HWND.
            </summary>
            <param name="hwnd">Our HWND that we need to destroy.</param>
            <seealso cref="M:System.Windows.Interop.HwndHost.DestroyWindowCore(System.Runtime.InteropServices.HandleRef)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.WndProc(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr,System.Boolean@)">
            <summary>
            This is overridden from <see cref="T:System.Windows.Interop.HwndHost"/> and is called to provide us with Win32 messages that are sent to our hwnd.
            </summary>
            <param name="hwnd">Window receiving the message (should always match our <see cref="P:System.Windows.Interop.HwndHost.Handle"/>).</param>
            <param name="msg">Indicates the message being received.  See Win32 documentation for WM_* constant values.</param>
            <param name="wParam">The "wParam" data being provided with the message.  Meaning varies by message.</param>
            <param name="lParam">The "lParam" data being provided with the message.  Meaning varies by message.</param>
            <param name="handled">If true then the message will not be forwarded to any (more) <see cref="E:System.Windows.Interop.HwndHost.MessageHook"/> handlers.</param>
            <returns>Return value varies by message.</returns>
            <seealso cref="M:System.Windows.Interop.HwndHost.WndProc(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr,System.Boolean@)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            We override the  <see cref="M:System.Windows.UIElement.OnGotFocus(System.Windows.RoutedEventArgs)"/> to prevent the focus event from propagating.
            We expect the event raised from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_GotFocus(System.Object,System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnLostFocus(System.Windows.RoutedEventArgs)">
            <summary>
            We override the  <see cref="M:System.Windows.UIElement.OnLostFocus(System.Windows.RoutedEventArgs)"/> to prevent the focus event from propagating.
            We expect the event raised from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_LostFocus(System.Object,System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnRender(System.Windows.Media.DrawingContext)">
            <summary>
            Override for painting to draw
            </summary>
            <param name="dc">The tools to handle the drawing via <see cref="T:System.Windows.Media.DrawingContext"/>.</param>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2InitializationCompleted">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.Microsoft#Web#WebView2#Wpf#IWebView2Private#InitializeController(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.Microsoft#Web#WebView2#Wpf#IWebView2Private#UninitializeController">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.Dispose(System.Boolean)">
            <summary>
            This is called by our base class according to the typical implementation of the <see cref="T:System.IDisposable"/> pattern.
            We implement it by releasing all of our underlying COM resources, including our <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/>.
            </summary>
            <param name="disposing">True if a caller is explicitly calling Dispose, false if we're being finalized.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.BeginInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Prevents the control from implicitly initializing its <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CoreWebView2"/> until <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EndInit"/> is called.
            Does *not* prevent explicit initialization of the CoreWebView2 (i.e. <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>).
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            Note that the "Initialize" in ISupportInitialize and the "Init" in BeginInit/EndInit mean
            something different and more general than this control's specific concept of initializing
            its CoreWebView2 (explicitly or implicitly).  This ISupportInitialize pattern is a general
            way to set batches of properties on the control to their initial values without triggering
            any dependent side effects until all of the values are set (i.e. until EndInit is called).
            In the case of this control, a specific side effect to be avoided is triggering implicit
            initialization of the CoreWebView2 when setting the Source property.
            For example, normally if you set <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CreationProperties"/> after you've already set Source,
            the data set to CreationProperties is ignored because implicit initialization has already started.
            However, if you set the two properties (in the same order) in between calls to BeginInit and
            EndInit then the implicit initialization of the CoreWebView2 is delayed until EndInit, so the data
            set to CreationProperties is still used even though it was set after Source.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.EndInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Invokes any functionality that has been delayed since the corresponding call to <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.BeginInit"/>.
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            See the documentation of <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.BeginInit"/> for more information.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.ReparentController(System.IntPtr,System.Boolean)">
            <summary>
            Changes our controller's ParentWindow to the given HWND, along with any other necessary associated work.
            </summary>
            <param name="hwnd">The new HWND to set as the controller's parent.  IntPtr.Zero means that the controller will have no parent and the CoreWebView2 will be hidden.</param>
            <param name="sync">Whether or not to call <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.SyncControllerWithParentWindow"/> as required.  Defaults to true.  If you pass false then you should call it yourself if required.</param>
            <remarks>
            Reparenting the controller isn't necessarily as simple as changing its ParentWindow property,
            and this method exists to ensure that any other work that needs to be done at the same time gets done.
            The reason that SyncControllerWithParentWindow isn't baked directly into this method is because
            sometimes we want to call the Sync functionality without necessarily reparenting (e.g. during initialization).
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.SyncControllerWithParentWindow">
            <summary>
            Syncs visual/windowing information between the controller and its parent HWND.
            This should be called any time a new, non-null HWND is set as the controller's parent,
            including when the controller is first created.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnWindowPositionChanged(System.Windows.Rect)">
            <summary>
            This is overridden from <see cref="T:System.Windows.Interop.HwndHost"/> and called when our control's location has changed.
            The HwndHost takes care of updating the HWND we created.
            What we need to do is move our CoreWebView2 to match the new location.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.SourceProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.Source"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.Source">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.SourceChanged">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.NavigationStarting">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.NavigationCompleted">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.CanGoBackProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CanGoBack"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.CanGoBack">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.CanGoForwardProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.CanGoForward"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.CanGoForward">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.TabIntoCore(System.Windows.Input.TraversalRequest)">
            <summary>
            This is overridden from <see cref="T:System.Windows.Interop.HwndHost"/> and is called to inform us that tabbing has caused the focus to move into our control/window.
            Since WPF can't manage the transition of focus to a non-WPF HWND, it delegates the transition to us here.
            So our job is just to place the focus in our external HWND.
            </summary>
            <param name="request">Information about how the focus is moving.</param>
            <returns><c>true</c> to indicate that we handled the navigation, or <c>false</c> to indicate that we didn't.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            This is overridden from <see cref="T:System.Windows.UIElement"/> and called to allow us to handle key press input.
            WPF should never actually call this in response to keyboard events because we're hosting a non-WPF window.
            When our window has focus Windows will send the input directly to it rather than to WPF's top-level window and input system.
            This override should only be called when we're explicitly forwarding accelerator key input from the CoreWebView2 to WPF (in CoreWebView2Controller_AcceleratorKeyPressed).
            Even then, this KeyDownEvent is only triggered because our PreviewKeyDownEvent implementation explicitly triggers it, matching WPF's usual system.
            So the process is:
            <list type="number">
            <item><description>CoreWebView2Controller_AcceleratorKeyPressed</description></item>
            <item><description>PreviewKeyDownEvent</description></item>
            <item><description>KeyDownEvent</description></item>
            <item><description>OnKeyDown</description></item>
            </list>
            .
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnKeyUp(System.Windows.Input.KeyEventArgs)">
            <summary>
            See <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnPreviewKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            This is the "Preview" (i.e. tunneling) version of <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>, so it actually happens first.
            Like OnKeyDown, this will only ever be called if we're explicitly forwarding key presses from the CoreWebView2.
            In order to mimic WPF's standard input handling, when we receive this we turn around and fire off the standard bubbling KeyDownEvent.
            That way others in the WPF tree see the same standard pair of input events that WPF itself would have triggered if it were handling the key press.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Wpf.WebView2.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.OnPreviewKeyUp(System.Windows.Input.KeyEventArgs)">
            <summary>
            See <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.OnPreviewKeyDown(System.Windows.Input.KeyEventArgs)"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.ZoomFactorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.ZoomFactor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.ZoomFactor">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.ZoomFactorChanged">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.DefaultBackgroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.DefaultBackgroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.DefaultBackgroundColor">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.AllowExternalDropProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="F:Microsoft.Web.WebView2.Wpf.WebView2.AllowExternalDropProperty"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.AllowExternalDrop">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2.DesignModeForegroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2.DesignModeForegroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.DesignModeForegroundColor">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.GoBack">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.GoForward">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.Reload">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.Stop">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.NavigateToString(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.ContentLoading">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2.ExecuteScriptAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2.WebMessageReceived">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2.IsInDesignMode">
            <summary>
            True when we're in design mode and shouldn't create an underlying CoreWebView2.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.CreationPropertiesProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CreationProperties"/> property.
            </summary>
            <seealso cref="T:System.Windows.DependencyProperty"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CreationProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.OnRender(System.Windows.Media.DrawingContext)">
            <summary>
            Design mode drawing content.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2InitializationCompleted">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_ProcessFailed(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's ProcessFailedEvent.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base._implicitInitGate">
            <summary>
            This is a "gate" which controls whether or not implicit initialization can occur.
            If implicit initialization is triggered while the gate is closed,
            then the initialization should be delayed until the gate opens.
            When we want to trigger implicit initialization we route the call through this gate.
            If the gate is open then the initialization will proceed.
            If the gate is closed then it will remember to trigger the initialization when it opens.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.BeginInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.EndInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.SetCurrentValueFromCore(System.Windows.DependencyProperty,System.Object)">
            <summary>
            Updates one of our dependency properties to match a new value from the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2"/>.
            It both sets the value and remembers (in _propertyChangingFromCore) that it came from the CoreWebView2 rather than the caller,
            allowing the property's "on changed" handler to alter its behavior based on where the new value came from.
            It's only intended to be called in a CoreWebView2 event handler that's informing us of a new property value.
            It's basically just a wrapper around the inherited SetCurrentValue which also maintains _propertyChangingFromCore.
            See the comments on <see cref="F:Microsoft.Web.WebView2.Wpf.WebView2Base._propertyChangingFromCore"/> for additional background info.
            One more thing worth explicitly stating is that it wraps SetCurrentValue rather than SetValue,
            in order to avoid overwriting any OneWay bindings that are set on the specified properties.
            Check the link https://stackoverflow.com/q/4230698 for more information about the difference between SetValue and SetCurrentValue.
            </summary>
            <param name="property">The property to change due to an equivalent change in the CoreWebView2.</param>
            <param name="value">The new value from the CoreWebView2.</param>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.IsPropertyChangingFromCore(System.Windows.DependencyProperty)">
            <summary>
            Checks if a given property is currently being updated to match an equivalent change in the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2"/>.
            This method should only be called from a property's "on changed" handler; it has no meaning at any other time.
            It is used to determine if the property is changing to match the CoreWebView2 or because the caller set it.
            Usually this is used in order to decide if the new value needs to be propagated down to the CoreWebView2.
            See the comments on <see cref="F:Microsoft.Web.WebView2.Wpf.WebView2Base._propertyChangingFromCore"/> for additional background info.
            </summary>
            <param name="property">The property to check.</param>
            <returns>True if the property is changing to match the CoreWebView2, or false if the property was changed by the caller.</returns>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.UIElement_IsVisibleChanged(System.Object,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This is a handler for our base UIElement's IsVisibleChanged event.
            It's predictably fired whenever IsVisible changes, and IsVisible reflects the actual current visibility status of the control.
            We just need to pass this info through to our CoreWebView2Controller so it can save some effort when the control isn't visible.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.SourceProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.Source"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.Source">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.SourcePropertyValid(System.Object)">
            <summary>
            This is a callback that WPF calls to validate a potential new Source value.
            </summary>
            <returns>
            True if the value is valid, false if it is not.
            If we return false then WPF should respond by throwing an <see cref="T:System.ArgumentException"/>.
            </returns>
            <remarks>
            Note that we unfortunately can't treat null as invalid here because null is valid prior to initialization.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.SourcePropertyChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This is a callback that WPF calls when the WPF Source property's value changes.
            This might have been triggered by either:
            1) The caller set Source to programmatically trigger a navigation.
            2) The CoreWebView changed its own source and we're just updating the dependency property to match.
            We use <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.IsPropertyChangingFromCore(System.Windows.DependencyProperty)"/> to distinguish the two cases.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.SourceChanged">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_SourceChanged(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2SourceChangedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's SourceChanged event.
            Unsurprisingly, it fires when the CoreWebView2's source URI has been changed.
            Note that there are two distinct triggers for this:
            1) The CoreWebView2 was told to navigate programmatically (potentially by us, see SourcePropertyChanged).
            2) The user interacted with the CoreWebView2, e.g. clicked a link.
            In either of the above cases, this event might trigger several times due to e.g. redirection.
            Aside from propagating to our own event, we just need to update our WPF Source property to match the CoreWebView2's.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.NavigationStarting">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_NavigationStarting(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's NavigationStarting event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.NavigationCompleted">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_NavigationCompleted(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's NavigationCompleted event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_HistoryChanged(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2's HistoryChanged event.
            We're handling it in order to update our WPF CanGoBack and CanGoForward properties.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoBackProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoBack"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoBack">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoForwardProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoForward"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.CanGoForward">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_MoveFocusRequested(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusRequestedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2Controller's MoveFocusRequested event.
            It fires when the CoreWebView2Controller has focus but wants to move it elsewhere in the app.
            E.g. this happens when the user tabs past the last item in the CoreWebView2 and focus needs to return to some other app control.
            So our job is just to tell WPF to move the focus on to the next control.
            Note that we don't propagate this event outward as a standard WPF routed event because we've implemented its purpose here.
            If users of the control want to track focus shifting in/out of the control, they should use standard WPF events.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_GotFocus(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2Controller's GotFocus event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_LostFocus(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2Controller's LostFocus event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_AcceleratorKeyPressed(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2Controller's AcceleratorKeyPressed event.
            This is called to inform us about key presses that are likely to have special behavior (e.g. esc, return, Function keys, letters with modifier keys).
            WPF can't detect this input because Windows sends it directly to the Win32 CoreWebView2Controller control.
            We implement this by generating standard WPF key input events, allowing callers to handle the input in the usual WPF way if they want.
            If nobody handles the WPF key events then we'll allow the default CoreWebView2Controller logic (if any) to handle it.
            Of the possible options, this implementation should provide the most flexibility to callers.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.ZoomFactorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.ZoomFactor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.ZoomFactor">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoerceZoomFactorPropertyChanged(System.Windows.DependencyObject,System.Object)">
            <summary>
            This is a callback that WPF calls when our WPF ZoomFactor property's value changes.
            This might have been triggered by either:
            1) The caller set ZoomFactor to change the zoom of the CoreWebView2.
            2) The CoreWebView2 changed its own ZoomFactor and we're just updating the dependency property to match.
            We use <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.IsPropertyChangingFromCore(System.Windows.DependencyProperty)"/> to distinguish the two cases.
            </summary>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.ZoomFactorChanged">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_ZoomFactorChanged(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2Controller's ZoomFactorChanged event.
            Unsurprisingly, it fires when the CoreWebView2Controller's ZoomFactor has been changed.
            Note that there are two distinct triggers for this:
            1) The value was changed programmatically (potentially by us, see ZoomFactorPropertyChanged).
            2) The user interacted with the CoreWebView2, e.g. CTRL + Mouse Wheel.
            Aside from propagating to our own event, we just need to update our WPF ZoomFactor property to match the CoreWebView2Controller's.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.DefaultBackgroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.DefaultBackgroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.DefaultBackgroundColor">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.DefaultBackgroundColorPropertyChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This is a callback that WPF calls when our WPF DefaultBackgroundColor property's value changes.
            Since CoreWebView2Controller does not update this property itself, this is only triggered by the
            caller setting DefaultBackgroundColor.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.AllowExternalDropProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="F:Microsoft.Web.WebView2.Wpf.WebView2Base.AllowExternalDropProperty"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.AllowExternalDrop">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.AllowExternalDropPropertyChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This is a callback that WPF calls when our WPF AllowExternalDrop property's value changes.
            Since CoreWebView2Controller does not update this property itself, this is only triggered by the
            caller setting AllowExternalDrop.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2Base.DesignModeForegroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2Base.DesignModeForegroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2Base.DesignModeForegroundColor">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.GoBack">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.GoForward">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.Reload">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.Stop">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.NavigateToString(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.ContentLoading">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_ContentLoading(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2ContentLoadingEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's ContentLoading event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.ExecuteScriptAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2Base.WebMessageReceived">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2_WebMessageReceived(System.Object,Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs)">
            <summary>
            This is an event handler for our CoreWebView2's WebMessageReceived event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.TabInto(System.Windows.Input.TraversalRequest)">
            <summary>
            Moves focus to the CoreWebView2Controller according to the <seealso cref="T:System.Windows.Input.TraversalRequest"/>
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.OnGotKeyboardFocus(System.Object,System.Windows.Input.KeyboardFocusChangedEventArgs)">
            <summary>
            This is an event handler for the <see cref="E:System.Windows.UIElement.GotKeyboardFocus"/> event.
            It is called to inform us when we receive the keyboard focus.
            We handle this by passing the keyboard focus on to the underlying <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2"/>.
            We never want to land in a state where our control actually has the keyboard focus.
            </summary>
            <param name="sender">The control that received keyboard focus.</param>
            <param name="e">Arguments from the underlying GotKeyboardFocus event.</param>
            <remarks>
            For WebView2 control, it's actually possible for us to receive keyboard focus without this method being called.
            One known case where that happens is when our parent window is deactivated while we have focus, then reactivated.
            We handle that case in <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2.WndProc(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr,System.Boolean@)"/>.
            </remarks>
            <seealso cref="M:Microsoft.Web.WebView2.Wpf.WebView2.WndProc(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr,System.Boolean@)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.Focus">
            <summary>
            Implementation of <see cref="M:System.Windows.UIElement.Focus"/> pattern.
            Developers should never reach the WebView2Base class here.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2Base.Dispose">
            <summary>
            Implementation of the <see cref="T:System.IDisposable"/> pattern.
            Should on be called during m_element's Dispose().
            When this is called, it means all resources releted to the control are disposed and we can safely remove the control from the dictionary.
            </summary>
        </member>
        <member name="T:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl">
             <summary>
             Visual hosting version of the WebView2 control.
             </summary>
             <remarks>
             This control is effectively a wrapper around the [WebView2 COM
             API](https://aka.ms/webview2). You can directly access the underlying
             ICoreWebView2 interface and all of its functionality by accessing the
             <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> property. Some of the most common COM
             functionality is also accessible directly through wrapper
             methods/properties/events on the control.
            
             Upon creation, the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> property will be
             <c>null</c>. This is because creating the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> is an
             expensive operation which involves things like launching Edge browser
             processes. There are two ways to cause the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> to
             be created:
             <list type="bullet">
             <item><description>
             Call the <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> method.  This is
             referred to as explicit initialization.
             </description></item>
             <item><description>
             Set the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Source"/> property (which could be done from
             markup, for example).  This is referred to as implicit initialization.
             Either option will start initialization in the background and return
             back to the caller without waiting for it to finish.
             To specify options regarding the initialization process, either pass
             your own <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2Environment"/> to <see
             cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> or set the control's <see
             cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CreationProperties"/> property prior to initialization.
             </description></item>
             </list>
            
             When initialization has finished (regardless of how it was triggered or
             whether it succeeded) then the following things will occur, in this
             order:
             <list type="number">
             <item><description>
             The control's <see cref="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2InitializationCompleted"/> event
             will be invoked. If you need to perform one time setup operations on
             the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> prior to its use then you should
             do so in a handler for that event.
             </description></item>
             <item><description>
             If initialization was successful and a Uri has been set to the <see
             cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Source"/> property then the control will start navigating to it in
             the background (i.e. these steps will continue without waiting for the
             navigation to finish).
             </description></item>
             <item><description>
             The Task returned from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/> will
             complete.
             </description></item>
             </list>
            
             For more details about any of the methods/properties/events involved in
             the initialization process, see its specific documentation.
            
             Because the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> is a very heavyweight
             object (potentially responsible for multiple running processes and
             megabytes of disk space) the control implements <see
             cref="T:System.IDisposable"/> to provide an explicit means to free it.
             Calling <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Dispose"/> will release the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/>
             and its underlying resources (except any that are also being used by other
             WebViews), and reset <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> to <c>null</c>. After <see
             cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Dispose"/> has been called the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> cannot be
             re-initialized, and any attempt to use functionality which requires it
             will throw an <see cref="T:System.ObjectDisposedException"/>.
            
             Accelerator key presses (e.g. Ctrl+P) that occur within the control will
             fire standard key press events such as OnKeyDown. You can suppress the
             control's default implementation of an accelerator key press (e.g.
             printing, in the case of Ctrl+P) by setting the Handled property of its
             EventArgs to true. Also note that the underlying browser process is
             blocked while these handlers execute, so:
             <list type="number">
             <item>
             <description>You should avoid doing a lot of work in these handlers.</description>
             </item>
             <item><description>
             Some of the WebView2 and CoreWebView2 APIs may throw errors if
             invoked within these handlers due to being unable to communicate with
             the browser process.
             </description></item>
             </list>
             If you need to do a lot of work and/or invoke WebView2 APIs in response to
             accelerator keys then consider kicking off a background task or queuing
             the work for later execution on the UI thread.
            
             This control extends <see cref="T:System.Windows.Controls.Control"/> in order to host the image
             displaying WebView's content using template. This has some
             implications regarding the control's input and output behavior as well as
             the functionality it "inherits" from <see cref="T:System.Windows.UIElement"/> and <see
             cref="T:System.Windows.FrameworkElement"/>.
            
             The content of <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl"/> is rendered by an <see cref="T:System.Windows.Controls.Image"/>.
             By default, <see cref="P:System.Windows.FrameworkElement.UseLayoutRounding"/> of WebView2CompositionControl
             is set to true. This can prevent the <see cref="T:System.Windows.Controls.Image"/> from becoming blurry at certain dimensions,
             but it disables anti-aliasing. Set it to false if you want to keep the anti-aliasing.
             
             Note that the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl"/> uses a [GraphicsCaptureSession](https://learn.microsoft.com/en-us/uwp/api/windows.graphics.capture.graphicscapturesession)
             to capture the screen content from the underlying browser processes.
             As such, you may experience lower framerates compared to the standard <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2"/> control, and DRM protected
             content will fail to play or display properly.
            
             </remarks>
             <seealso cref="T:System.Windows.Controls.Control"/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.PartImageName">
            <summary>
            TemplatePart Name constant for the Image used to represent WebView2.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl._image">
            <summary>
            Image control uses to display content of WebView2.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl._d3dImage">
            <summary>
            Component used to capture from WebView visual and work as source of Image.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnApplyTemplate">
            <summary>
            <see cref="M:System.Windows.FrameworkElement.OnApplyTemplate"/> interface.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.#cctor">
            <summary>
            Static constructor for the WebView2CompositionControl class.
            </summary>
            <remarks>
            This static constructor is responsible for overriding the default style key property
            for instances of the WebView2CompositionControl class. It sets the metadata to use
            the type of the WebView2CompositionControl as the default style key.
            See href="https://learn.microsoft.com/en-us/dotnet/api/system.windows.frameworkelement.defaultstylekey?view=netframework-4.8.1"/>
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.#ctor">
            <summary>
            Creates a new instance of a WebView2Composition control.
            Note that the control's <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> will be null until initialized.
            See the <see cref="T:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl"/> class documentation for an initialization overview.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.WebView2CompositionControl_SizeChanged(System.Object,System.Windows.SizeChangedEventArgs)">
            <summary>
            This is an event handler for WPF control's SizeChanged event.
            We use CoreWebView2Controller.NotifyParentWindowPositionChanged to notify our browser our
            size and relative location has changed.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.WebView2CompositionControl_LocationChanged(System.Object,System.EventArgs)">
            <summary>
            This is an event handler for WPF control's LocationChange event.
            We use CoreWebView2Controller.NotifyParentWindowPositionChanged to notify our browser our
            position has changed.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.InitializeCoreDispatcher">
            <summary>
            Initialize the dispatcher queue.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2Controller_CursorChanged(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2CompositionController's CursorChanged event.
            We use CursorInteropHelper to Obtain a WPF Cursor from the provided Win32 Handle.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)">
            <summary>
            Send the pointer event to the WebView2 Control.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendPointerInput(Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind,Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnTouchDown(System.Windows.Input.TouchEventArgs)">
            <summary>
            This is an event handler for WPF control's OnTouchDown event.
            We use CoreWebView2CompositionController.SendPointerInput to send the touch input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnTouchMove(System.Windows.Input.TouchEventArgs)">
            <summary>
            This is an event handler for WPF control's OnTouchMove event.
            We use CoreWebView2CompositionController.SendPointerInput to send the touch input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnTouchUp(System.Windows.Input.TouchEventArgs)">
            <summary>
            This is an event handler for WPF control's OnTouchUp event.
            We use CoreWebView2CompositionController.SendPointerInput to send the touch input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.GetMouseEventVirtualKeys(System.Windows.Input.MouseEventArgs)">
            <summary>
            Helper function to get the <see cref="T:Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys"/> of the mouse event.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)">
            <summary>
            Send the mouse event to the WebView2 Control.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Core.CoreWebView2CompositionController.SendMouseInput(Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind,Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys,System.UInt32,System.Drawing.Point)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <summary>
            This is an event handler for WPF control's OnMouseMove event.
            We use CoreWebView2CompositionController.SendMouseInput to send the mouse input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMouseDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            This is an event handler for WPF control's OnMouseDown event.
            We use CoreWebView2CompositionController.SendMouseInput to send the mouse input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMouseUp(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            This is an event handler for WPF control's OnMouseUp event.
            We use CoreWebView2CompositionController.SendMouseInput to send the mouse input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMouseWheel(System.Windows.Input.MouseWheelEventArgs)">
            <summary>
            This is an event handler for WPF control's OnMouseWheel event.
            We use CoreWebView2CompositionController.SendMouseInput to send the input to our browser.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMouseDoubleClick(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            This is an event handler for WPF control's OnMouseDoubleClick event.
            We use CoreWebView2CompositionController.SendMouseInput to send the input to our browser.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CreationPropertiesProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CreationProperties"/> property.
            </summary>
            <seealso cref="T:System.Windows.DependencyProperty"/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CreationProperties">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnRender(System.Windows.Media.DrawingContext)">
            <summary>
            Override for painting to draw
            </summary>
            <param name="dc">The tools to handle the drawing via <see cref="T:System.Windows.Media.DrawingContext"/>.</param>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2InitializationCompleted">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Microsoft#Web#WebView2#Wpf#IWebView2Private#InitializeController(System.IntPtr,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Microsoft#Web#WebView2#Wpf#IWebView2Private#UninitializeController">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Dispose">
            <summary>
            Implementation of the <see cref="T:System.IDisposable"/> pattern.
            This will release all of our underlying COM resources.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.BeginInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Prevents the control from implicitly initializing its <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2"/> until <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EndInit"/> is called.
            Does *not* prevent explicit initialization of the CoreWebView2 (i.e. <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EnsureCoreWebView2Async(Microsoft.Web.WebView2.Core.CoreWebView2Environment,Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions)"/>).
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            Note that the "Initialize" in ISupportInitialize and the "Init" in BeginInit/EndInit mean
            something different and more general than this control's specific concept of initializing
            its CoreWebView2 (explicitly or implicitly).  This ISupportInitialize pattern is a general
            way to set batches of properties on the control to their initial values without triggering
            any dependent side effects until all of the values are set (i.e. until EndInit is called).
            In the case of this control, a specific side effect to be avoided is triggering implicit
            initialization of the CoreWebView2 when setting the Source property.
            For example, normally if you set <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CreationProperties"/> after you've already set Source,
            the data set to CreationProperties is ignored because implicit initialization has already started.
            However, if you set the two properties (in the same order) in between calls to BeginInit and
            EndInit then the implicit initialization of the CoreWebView2 is delayed until EndInit, so the data
            set to CreationProperties is still used even though it was set after Source.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.EndInit">
            <summary>
            Implementation of the ISupportInitialize pattern.
            Invokes any functionality that has been delayed since the corresponding call to <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.BeginInit"/>.
            Mainly intended for use by interactive UI designers.
            </summary>
            <remarks>
            See the documentation of <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.BeginInit"/> for more information.
            </remarks>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.SourceProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Source"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Source">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.SourceChanged">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.NavigationStarting">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.NavigationCompleted">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoBackProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoBack"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoBack">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoForwardProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoForward"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CanGoForward">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.KeyboardInputSite">
            <summary>
            WebView2Composition Control only needs IKeyboardInputSink:TabInto to get the direction of tab traversal.
            KeyboardInputSite is not implemented by WebView2Composition.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnGotFocus(System.Windows.RoutedEventArgs)">
            <summary>
            We override the  <see cref="M:System.Windows.UIElement.OnGotFocus(System.Windows.RoutedEventArgs)"/> to prevent the focus event from propagating.
            We expect the event raised from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_GotFocus(System.Object,System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnLostFocus(System.Windows.RoutedEventArgs)">
            <summary>
            We override the  <see cref="M:System.Windows.UIElement.OnLostFocus(System.Windows.RoutedEventArgs)"/> to prevent the focus event from propagating.
            We expect the event raised from <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2Base.CoreWebView2Controller_LostFocus(System.Object,System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2Controller_GotFocus(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2Controller's GotFocus event.
            Used to record whether the control currently has focus.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.CoreWebView2Controller_LostFocus(System.Object,System.Object)">
            <summary>
            This is an event handler for our CoreWebView2Controller's Lost event.
            We just need to propagate the event to WPF.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.HasFocusWithin">
            <summary>
            IKeyboardInputSink:HasFocusWithin interface.
            Whether WebView has focus.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnMnemonic(System.Windows.Interop.MSG@,System.Windows.Input.ModifierKeys)">
            <summary>
            IKeyboardInputSink:OnMnemonic interface.
            Not implemented by WebView2.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.RegisterKeyboardInputSink(System.Windows.Interop.IKeyboardInputSink)">
            <summary>
            IKeyboardInputSink:RegisterKeyboardInputSink interface.
            Not implemented by WebView2.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.TabInto(System.Windows.Input.TraversalRequest)">
            <summary>
            IKeyboardInputSink:TabInto interface.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.TranslateAccelerator(System.Windows.Interop.MSG@,System.Windows.Input.ModifierKeys)">
            <summary>
            IKeyboardInputSink:TranslateAccelerator interface.
            Not implemented by WebView2.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.TranslateChar(System.Windows.Interop.MSG@,System.Windows.Input.ModifierKeys)">
            <summary>
            IKeyboardInputSink:TranslateChar interface.
            Not implemented by WebView2.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            This is overridden from <see cref="T:System.Windows.UIElement"/> and called to allow us to handle key press input.
            WPF should never actually call this in response to keyboard events because the focus is on the controller's HWND.
            When Controller's HWND has focus, WPF does not know the Controller's HWND belongs to this control, and the key event will not be fired for this control and WPF main window.
            This override should only be called when we're explicitly forwarding accelerator key input from the CoreWebView2 to WPF (in CoreWebView2Controller_AcceleratorKeyPressed).
            Even then, this KeyDownEvent is only triggered because our PreviewKeyDownEvent implementation explicitly triggers it, matching WPF's usual system.
            So the process is:
            <list type="number">
            <item><description>CoreWebView2Controller_AcceleratorKeyPressed</description></item>
            <item><description>PreviewKeyDownEvent</description></item>
            <item><description>KeyDownEvent</description></item>
            <item><description>OnKeyDown</description></item>
            </list>
            .
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnKeyUp(System.Windows.Input.KeyEventArgs)">
            <summary>
            See <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnPreviewKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            This is the "Preview" (i.e. tunneling) version of <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>, so it actually happens first.
            Like OnKeyDown, this will only ever be called if we're explicitly forwarding key presses from the CoreWebView2.
            In order to mimic WPF's standard input handling, when we receive this we turn around and fire off the standard bubbling KeyDownEvent.
            That way others in the WPF tree see the same standard pair of input events that WPF itself would have triggered if it were handling the key press.
            </summary>
            <seealso cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnKeyDown(System.Windows.Input.KeyEventArgs)"/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnPreviewKeyUp(System.Windows.Input.KeyEventArgs)">
            <summary>
            See <see cref="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.OnPreviewKeyDown(System.Windows.Input.KeyEventArgs)"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ZoomFactorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ZoomFactor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ZoomFactor">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ZoomFactorChanged">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DefaultBackgroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DefaultBackgroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DefaultBackgroundColor">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.AllowExternalDropProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.AllowExternalDropProperty"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.AllowExternalDrop">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DesignModeForegroundColorProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DesignModeForegroundColor"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.DesignModeForegroundColor">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.FpsDividerProperty">
            <summary>
            The WPF <see cref="T:System.Windows.DependencyProperty"/> which backs the <see cref="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.FpsDivider"/> property.
            </summary>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.FpsDivider">
            <summary>
            Gets or sets the divider for the rendering frame rate of the WebViewCompositionControl.
            </summary>
            <remarks>
            The FpsDivider property affects how the rendering frame rate is divided. For example, if the default rendering frame rate of the content is 60 frames
            per second (fps), setting the FpsDivider to 2 reduces the frame rate to 30 fps. This property is useful for reducing the rendering load and improving
            performance for scenarios where high frame rates are unnecessary.
            </remarks>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.FpsDividerChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This is a callback that WPF calls when our WPF FpsDivider property's value changes.
            We implement it by setting the FpsDivider of the GraphicsItemD3DImage.
            </summary>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.GoBack">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.GoForward">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Reload">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.Stop">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.NavigateToString(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ContentLoading">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.ExecuteScriptAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="E:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.WebMessageReceived">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Web.WebView2.Wpf.WebView2CompositionControl.IsInDesignMode">
            <summary>
            True when we're in design mode and shouldn't create an underlying CoreWebView2.
            </summary>
        </member>
    </members>
</doc>
