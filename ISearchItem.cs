﻿using HIH.Framework.AutoCrawingManager.Result;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager
{
    public class ISearchItem
    {
        public string OrderNumber { get; set; }
        public string SearchKey { get; set; }
        public string BLNo { get; set; }
        public string ShippingCompany { get; set; } //船公司
        public string PriETA { get; set; }  //数据库中的ETA
        public string SeaETA { get; set; } //搜索到的ETA
        public string ShowDetail { get; set; }
        public string Result { get; set; } //最后结果
        public bool Option { get; set; }
        public BindingList<IResultContainer> ContainerList { get; set; }
        public QUESTATUS Status { get; set; } //状态
        public string LastTimeDesc { get; set; } //最新时间描述
        public string LastTime
        {
            get => _LastTime; set
            {
                this._LastTime = value;
                this.LastTimeDesc = this.GetTimeAgo(value);

            }
        }

        private string _LastTime; //最后更新时间

        public ISearchItem()
        {
            this.Status = QUESTATUS.WAIT;
            this.Option = false;
        }

        private string GetTimeAgo(string pastDateStr)
        {
            DateTime pastDate;
            DateTime now = DateTime.Now;

            if (!DateTime.TryParse(pastDateStr, out pastDate))
            {
                return "未有更新";
            }

            TimeSpan difference = now - pastDate;

            if (difference.Days < 1)
                return "刚刚";
            else if (difference.Days == 1)
                return "一天前";
            else if (difference.Days < 7)
                return $"{difference.Days}天前";
            else if (difference.Days < 30)
                return "一周前"; // 简化处理为超过一天小于30天都返回"一周前"
            else if (difference.Days <= 30)
                return "30天前";
            else
                return "超过30天";
        }
    }
    public enum QUESTATUS
    {
        WAIT,
        SUCCESS,
        FAIL
    }

}
