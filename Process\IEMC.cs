﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IEMC : IProcess
    {
        private readonly string SearchMainPage = "https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do";

        public IEMC():base("EMC","EGLV"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = $"document.getElementById('NO').value='{searchKey}';";
            thiPro.JScript = "javascript:frmSubmit(13,2);";
            fouPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                string tablePattern = @"<table[^>]*class=""ec-table ec-table-sm""[^>]*>(.*?)</table>";

                MatchCollection containerMatches = Regex.Matches(containerString, tablePattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    string tableContainerString = containerMatch.Value;
                    if (tableContainerString.Contains("Container(s)"))
                    {
                        string trPattern = @"<tr[^>]*>(.*?)<\/tr>";
                        MatchCollection trMatches = Regex.Matches(tableContainerString, trPattern, RegexOptions.Singleline);
                        string containerNo = "";
                        string returnDateString = "";
                        string pickupDateString = "";
                        string uploadDateString = "";
                        foreach (Match trMatch in trMatches)
                        {
                            string tdPattern = @"<td[^>]*>(.*?)<\/td>";
                            MatchCollection tdMatches = Regex.Matches(trMatch.Value, tdPattern, RegexOptions.Singleline);

                            if (tdMatches.Count > 8)
                            {
                                string aPattern = @"<a[^>]*>(.*?)<\/a>";
                                MatchCollection aMatches = Regex.Matches(tdMatches[0].Groups[1].Value, aPattern, RegexOptions.Singleline);

                                if (aMatches.Count > 0)
                                {
                                    containerNo = aMatches[0].Groups[1].Value;
                                }

                                if (tdMatches[7].Groups[1].Value.StartsWith("Discharged"))
                                {
                                    uploadDateString = this.ParseExactTime(tdMatches[8].Groups[1].Value);
                                }
                                if (tdMatches[7].Groups[1].Value.StartsWith("Pick-up"))
                                {
                                    pickupDateString = this.ParseExactTime(tdMatches[8].Groups[1].Value);
                                }
                                if (tdMatches[7].Groups[1].Value.StartsWith("Empty container returned"))
                                {
                                    returnDateString = this.ParseExactTime(tdMatches[8].Groups[1].Value);
                                }
                            }
                            if (!string.IsNullOrEmpty(containerNo))
                            {
                                IResultContainer containerItem = new IResultContainer();
                                Console.WriteLine(containerNo + "-" + pickupDateString + "-" + returnDateString);
                                containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                                containerList.Add(containerItem);
                            }
                        }

                        break;
                    }


                }
                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {

                string contentPattern = @"<td[^>]*class=""ec-fs-16b""[^>]*>(.*?)<\/td>";
                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);
                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<font[^>]*>(.*?)</font>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = trMatches[0].Groups[1].Value;

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("ETA为空");

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                // 定义输入日期的格式
                string inputFormat = "MMM-dd-yyyy";

                // 定义输出日期的格式
                string outputFormat = "yyyy-MM-dd";

                // 解析日期
                DateTime date;
                if (DateTime.TryParseExact(inputDate, inputFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
                {
                    // 转换并返回日期
                    return date.ToString(outputFormat, CultureInfo.InvariantCulture);
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }


            }
            catch (Exception exc)
            {
                throw;
            }
        }




    }
}
