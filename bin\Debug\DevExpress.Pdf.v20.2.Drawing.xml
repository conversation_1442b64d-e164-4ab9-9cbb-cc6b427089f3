<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.Pdf.v20.2.Drawing</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Pdf">
      <summary>
        <para>Contains classes and enumerations that are used to implement the main functionality of WinForms and WPF PDF Viewers, and the PDF Document API.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerAnnotation">
      <summary>
        <para>Implements options used to obtain information common for annotation types.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.Bounds">
      <summary>
        <para>Gets the annotation&#39;s bounds on the page.</para>
      </summary>
      <value>Annotation bounds in the page coordinate system.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.Color">
      <summary>
        <para>Gets the annotation color.</para>
      </summary>
      <value>The annotation color.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.Contents">
      <summary>
        <para>Gets the annotation contents.</para>
      </summary>
      <value>The annotation contents.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.ModificationDate">
      <summary>
        <para>Gets the date and time of the annotation&#39;s last modification.</para>
      </summary>
      <value>The date and time of the annotation&#39;s last modification.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.Name">
      <summary>
        <para>Gets the annotation&#39;s name.</para>
      </summary>
      <value>The annotation name.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.PageNumber">
      <summary>
        <para>Gets the page number where the annotation is located.</para>
      </summary>
      <value>The page number.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotation.Type">
      <summary>
        <para>Gets the annotation&#39;s type.</para>
      </summary>
      <value>One of enumeration values that indicates the annotation type.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerAnnotationBuilder">
      <summary>
        <para>Implements options used to build all annotation types.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.AnnotationType">
      <summary>
        <para>Gets the annotation&#39;s type.</para>
      </summary>
      <value>An enumeration value that indicates the annotation type.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.Bounds">
      <summary>
        <para>Gets the annotation&#39;s bounds on a page.</para>
      </summary>
      <value>Annotation bounds in the page coordinate system.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.Color">
      <summary>
        <para>Specifies the annotation&#39;s color.</para>
      </summary>
      <value>The markup annotation color.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.Contents">
      <summary>
        <para>Specifies the annotation contents.</para>
      </summary>
      <value>The annotation contents.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.IsSelected">
      <summary>
        <para>Specifies whether or not to select the annotation.</para>
      </summary>
      <value>true, to select the annotation; otherwise, false (the default value).</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.ModificationDate">
      <summary>
        <para>Specifies the date and time of the annotation&#39;s last modification.</para>
      </summary>
      <value>The date and time when the annotation was last modified.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.Name">
      <summary>
        <para>Specifies the annotation name.</para>
      </summary>
      <value>The name of the annotation.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerAnnotationBuilder.PageNumber">
      <summary>
        <para>Gets the number of the page on which the annotation is located.</para>
      </summary>
      <value>The page number.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerMarkupAnnotation">
      <summary>
        <para>Implements properties used to return information about markup annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotation.Author">
      <summary>
        <para>Gets the markup annotation&#39;s author.</para>
      </summary>
      <value>The annotation author.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotation.CreationDate">
      <summary>
        <para>Gets the date and time when the markup annotation was created.</para>
      </summary>
      <value>The date and time of the markup annotation&#39;s creation.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotation.Opacity">
      <summary>
        <para>Gets the markup annotation opacity.</para>
      </summary>
      <value>The markup annotation opacity (from 0 to 1).</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotation.Subject">
      <summary>
        <para>Gets the markup annotation&#39;s subject.</para>
      </summary>
      <value>A short description of the subject.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder">
      <summary>
        <para>Implements properties used to build markup annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder.Author">
      <summary>
        <para>Gets or sets the author of a markup annotation&#39;s author.</para>
      </summary>
      <value>The author&#39;s name.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder.CreationDate">
      <summary>
        <para>Specifies the date and time when the markup annotation was created.</para>
      </summary>
      <value>The annotation&#39;s creation date.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder.Opacity">
      <summary>
        <para>Specifies the markup annotation opacity.</para>
      </summary>
      <value>Markup annotation opacity (from 0 to 1).</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder.Subject">
      <summary>
        <para>Gets or sets a subject the markup annotation addresses.&#39;</para>
      </summary>
      <value>A short description of the subject.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerTextAnnotation">
      <summary>
        <para>Contains properties used to return information about text annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextAnnotation.IconName">
      <summary>
        <para>Gets the name of the annotation&#39;s icon.</para>
      </summary>
      <value>The annotation icon&#39;s name.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerTextAnnotationBuilder">
      <summary>
        <para>Contains options used to build text annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextAnnotationBuilder.IconName">
      <summary>
        <para>Gets the name of the annotation&#39;s icon.</para>
      </summary>
      <value>The icon name.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerTextMarkupAnnotation">
      <summary>
        <para>Contains properties used to return information about text markup annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextMarkupAnnotation.Quads">
      <summary>
        <para>Gets a collection of quadrilateral points that encompass the annotation area.</para>
      </summary>
      <value>A list of quadrilateral points.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextMarkupAnnotation.Style">
      <summary>
        <para>Gets the style of a text markup annotation.</para>
      </summary>
      <value>An enumeration value that indicates the text markup style.</value>
    </member>
    <member name="T:DevExpress.Pdf.IPdfViewerTextMarkupAnnotationBuilder">
      <summary>
        <para>Implements options used to build text markup annotations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextMarkupAnnotationBuilder.Quads">
      <summary>
        <para>Gets a collection of quadrilateral points that encompass the annotation area.</para>
      </summary>
      <value>A list of the quadrilateral points.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextMarkupAnnotationBuilder.SelectedText">
      <summary>
        <para>Gets a selected text for which a markup annotation is created.</para>
      </summary>
      <value>The selected text.</value>
    </member>
    <member name="P:DevExpress.Pdf.IPdfViewerTextMarkupAnnotationBuilder.Style">
      <summary>
        <para>Gets or sets the style of a text markup annotation.</para>
      </summary>
      <value>The enumeration value that specifies the annotation style.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfCommentFilter">
      <summary>
        <para>Contains filters that can be applied to annotations.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCommentFilter.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfCommentFilter"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.Authors">
      <summary>
        <para>Retrieves a list of authors whose annotations should be displayed.</para>
      </summary>
      <value>A list of annotation authors.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.Checked">
      <summary>
        <para>Specifies whether to show checked annotations.</para>
      </summary>
      <value>true to apply this filter; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfCommentFilter.Clear">
      <summary>
        <para>Clears all applied filters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfCommentFilter.ClearWithoutNotification">
      <summary>
        <para>Clears all applied filters without notification.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Pdf.PdfCommentFilter.FilterChanged">
      <summary>
        <para>Fires when the filter has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.HideAll">
      <summary>
        <para>Specifies whether to hide all annotations and comments.</para>
      </summary>
      <value>true to hide all annotations; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.NoneStatus">
      <summary>
        <para>Specifies whether to show annotations whose review status is None.</para>
      </summary>
      <value>true to apply this filter; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.SearchText">
      <summary>
        <para>Gets or sets the text used to filter annotations.</para>
      </summary>
      <value>The text that annotations should contain.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.Statuses">
      <summary>
        <para>Retrieves a list of statuses by which to filter the annotations.</para>
      </summary>
      <value>A list of statuses.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfCommentFilter.Types">
      <summary>
        <para>Retrieves a list of types by which to filter the annotations.</para>
      </summary>
      <value>A list of types.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfCommentSortMode">
      <summary>
        <para>Lists values used to specify the annotation&#39;s sort mode in the Comments pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCommentSortMode.Author">
      <summary>
        <para>Annotations are sorted by author.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCommentSortMode.Checked">
      <summary>
        <para>Annotations are sorted by checkmark status (checked or unchecked).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCommentSortMode.Date">
      <summary>
        <para>Annotations are sorted by date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCommentSortMode.Page">
      <summary>
        <para>Annotations are sorted by page.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfCommentSortMode.Type">
      <summary>
        <para>Annotations are sorted by type.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphics">
      <summary>
        <para>Implements drawing on a PDF page.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddFormField(DevExpress.Pdf.PdfGraphicsAcroFormField)">
      <summary>
        <para>Adds an interactive form field to PDF graphics.</para>
      </summary>
      <param name="field">A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormField"/> object that represents an interactive form field that should be added to PDF graphics.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddLinkToPage(System.Drawing.RectangleF,System.Int32)">
      <summary>
        <para>Adds a link to the page with the specified page number.</para>
      </summary>
      <param name="linkArea">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the area on the page where a link is added.</param>
      <param name="pageNumber">An integer value specifying the page number.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddLinkToPage(System.Drawing.RectangleF,System.Int32,System.Single)">
      <summary>
        <para>Adds a link to the page with specified page number and zoom factor.</para>
      </summary>
      <param name="linkArea">A System.Drawing.RectangleF structure that specifies the area on the page where a link is added.</param>
      <param name="pageNumber">An integer value specifying the page number.</param>
      <param name="zoom">The zoom level by which the document is zoomed after a link is clicked on the page.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddLinkToPage(System.Drawing.RectangleF,System.Int32,System.Single,System.Single)">
      <summary>
        <para>Adds a link to the page with the specified page number and page destination.</para>
      </summary>
      <param name="linkArea">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the area on the page where a link is added.</param>
      <param name="pageNumber">An integer value specifying the page number.</param>
      <param name="destinationX">A <see cref="T:System.Single"/> object that is the horizontal coordinate of a page destination where the link is referred to.</param>
      <param name="destinationY">A <see cref="T:System.Single"/> object that is the vertical coordinate of a page destination where the link is referred to.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddLinkToPage(System.Drawing.RectangleF,System.Int32,System.Single,System.Single,System.Single)">
      <summary>
        <para>Adds a link to the page with the specified page number, page destination, and zoom factor.</para>
      </summary>
      <param name="linkArea">A System.Drawing.RectangleF structure that specifies the area on the page where a link is added.</param>
      <param name="pageNumber">An integer value specifying the page number.</param>
      <param name="destinationX">A System.Single object that is the horizontal coordinate of a page destination where the link is referred to.</param>
      <param name="destinationY">A System.Single object that is the vertical coordinate of a page destination where the link is referred to.</param>
      <param name="zoom">The zoom level by which the document is zoomed after a link is clicked on the page.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddLinkToUri(System.Drawing.RectangleF,System.Uri)">
      <summary>
        <para>Adds a link to the Uri at the specified link area on the page.</para>
      </summary>
      <param name="linkArea">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the area on the page where a link is added.</param>
      <param name="uri">A <see cref="T:System.Uri"/> object that is the link URI.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddToPageBackground(DevExpress.Pdf.PdfPage)">
      <summary>
        <para>Adds graphics to a PDF page background.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page in the document where graphics are drawn (measured in points - 1/72 of an inch).</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddToPageBackground(DevExpress.Pdf.PdfPage,System.Single,System.Single)">
      <summary>
        <para>Adds graphics to a PDF page background with the specified horizontal and vertical resolutions.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page where graphics are drawn (measured in points - 1/72 of an inch).</param>
      <param name="dpiX">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the horizontal resolution.</param>
      <param name="dpiY">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the vertical resolution.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddToPageForeground(DevExpress.Pdf.PdfPage)">
      <summary>
        <para>Adds graphics to a PDF page foreground.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page in the document where graphics are drawn (measured in points - 1/72 of an inch).</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.AddToPageForeground(DevExpress.Pdf.PdfPage,System.Single,System.Single)">
      <summary>
        <para>Adds graphics to a PDF page foreground with the specified horizontal and vertical resolutions.</para>
      </summary>
      <param name="page">A <see cref="T:DevExpress.Pdf.PdfPage"/> object that is the page in the document where graphics are drawn (measured in points - 1/72 of an inch).</param>
      <param name="dpiX">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the horizontal resolution.</param>
      <param name="dpiY">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the vertical resolution.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.ClearFormFields">
      <summary>
        <para>Clears all interactive form fields that were previously added to PDF graphics.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphics.ConvertImagesToJpeg">
      <summary>
        <para>Specifies whether to convert bitmap images to the Jpeg format reducing the size of the resulting PDF document.</para>
      </summary>
      <value>true, to convert bitmap images to the Jpeg format; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphics.DefaultDpi">
      <summary>
        <para>The default DPI of a page that is the default size to display text and other content on the page.</para>
      </summary>
      <value>A DPI value.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawBezier(System.Drawing.Pen,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Draws a Bezier spline defined by four Point structures.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> structure that determines the color, width, and style of the curve.</param>
      <param name="pt1">A <see cref="T:System.Drawing.Point"/> structure that is the starting point of the curve.</param>
      <param name="pt2">A <see cref="T:System.Drawing.Point"/> structure that is the first control point of the curve.</param>
      <param name="pt3">A <see cref="T:System.Drawing.Point"/> structure that is the second control point of the curve.</param>
      <param name="pt4">A <see cref="T:System.Drawing.Point"/> structure that is the final point of the curve.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawBeziers(System.Drawing.Pen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a series of Bezier splines from an array of PointF structures.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> structure that determines the color, width, and style of the curve.</param>
      <param name="points">An array of <see cref="T:System.Drawing.PointF"/> structures that represent the points that determine the curve.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawEllipse(System.Drawing.Pen,System.Drawing.RectangleF)">
      <summary>
        <para>Draws an ellipse specified by a RectangleF structure.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> structure that determines the color, width, and style of the ellipse.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> structure that represents the boundaries of the ellipse.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.Byte[],System.Drawing.PointF)">
      <summary>
        <para>Draws the specified image using the byte array data at the specified location.</para>
      </summary>
      <param name="data">An array of bytes representing an image to draw.</param>
      <param name="location">A <see cref="T:System.Drawing.PointF"/> structure that represents the image location.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.Byte[],System.Drawing.RectangleF)">
      <summary>
        <para>Draws an image using an array of bytes representing image data and the image bounds.</para>
      </summary>
      <param name="data">An array of bytes representing an image to draw.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds used to draw an image.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.Drawing.Image,System.Drawing.PointF)">
      <summary>
        <para>Draws the specified image in its original size at the specified location.</para>
      </summary>
      <param name="image">A <see cref="T:System.Drawing.Image"/> object that specifies the image to draw.</param>
      <param name="location">A <see cref="T:System.Drawing.PointF"/> structure that represents the image location.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.Drawing.Image,System.Drawing.RectangleF)">
      <summary>
        <para>Draws an image using the specified image bounds.</para>
      </summary>
      <param name="image">An <see cref="T:System.Drawing.Image"/> object that specifies the image to draw.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the image bounds.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.Drawing.Image,System.Drawing.RectangleF,System.Drawing.RectangleF,System.Drawing.GraphicsUnit)">
      <summary>
        <para>Draws the portion of the specified Image at the specified location in the specified size.</para>
      </summary>
      <param name="image">An <see cref="T:System.Drawing.Image"/> object that specifies the image to draw.</param>
      <param name="destRect">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the location and size of the drawn image. The image is scaled to fit the rectangle.</param>
      <param name="srcRect">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the portion of the image object to draw.</param>
      <param name="srcUnit">A <see cref="T:System.Drawing.GraphicsUnit"/> enumeration that is the units of measure used by the srcRect parameter.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.IO.Stream,System.Drawing.PointF)">
      <summary>
        <para>Draws an image at the specified location using the stream data.</para>
      </summary>
      <param name="data">A <see cref="T:System.IO.Stream"/> class descendant, specifying the stream with the image data.</param>
      <param name="location">A <see cref="T:System.Drawing.PointF"/> structure that represents the image location.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawImage(System.IO.Stream,System.Drawing.RectangleF)">
      <summary>
        <para>Draws an image with the bounds and stream.</para>
      </summary>
      <param name="data">A <see cref="T:System.IO.Stream"/> class descendant, specifying the stream with the image data.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the bounds used to draw an image.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawLine(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Draws a line connecting two points on the page specified by the coordinate pairs.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> object that determines the color, width, and style of the line.</param>
      <param name="x1">A <see cref="T:System.Single"/> object that represents the x-coordinate of the first point.</param>
      <param name="y1">A <see cref="T:System.Single"/> object that represents the y-coordinate of the first point.</param>
      <param name="x2">A <see cref="T:System.Single"/> object that represents the x-coordinate of the second point.</param>
      <param name="y2">A <see cref="T:System.Single"/> object that represents the y-coordinate of the second point.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawLines(System.Drawing.Pen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a series of line segments that connect an array of PointF structures.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> object that determines the color, width, and style of the line segments.</param>
      <param name="points">An array of <see cref="T:System.Drawing.PointF"/> structures that represent the points to connect.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawPageContent(DevExpress.Pdf.PdfPage)">
      <summary>
        <para>Draws the specified page&#39;s content.</para>
      </summary>
      <param name="source">The source page from which content is drawn.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawPath(System.Drawing.Pen,System.Drawing.Drawing2D.GraphicsPath)">
      <summary>
        <para>Draws a path on a page.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> object that determines the color, width, and style of the path.</param>
      <param name="path">A <see cref="T:System.Drawing.Drawing2D.GraphicsPath"/> object to draw.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawPolygon(System.Drawing.Pen,System.Drawing.PointF[])">
      <summary>
        <para>Draws a polygon defined by an array of PointF structures.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> object that determines the color, width, and style of the polygon.</param>
      <param name="points">An array of <see cref="T:System.Drawing.PointF"/> structures that represent the vertices of the polygon.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawRectangle(System.Drawing.Pen,System.Drawing.RectangleF)">
      <summary>
        <para>Draws a rectangle specified by a Rectangle structure.</para>
      </summary>
      <param name="pen">A <see cref="T:System.Drawing.Pen"/> object that determines the color, width, and style of the rectangle.</param>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle"/> structure that represents the rectangle to draw.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Drawing.PointF)">
      <summary>
        <para>A set of overloaded methods used to draw the text string with the specified SolidBrush, Font objects and other parameters.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="point">A <see cref="T:System.Drawing.PointF"/> structure that specifies the upper-left corner of the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Drawing.PointF,DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Draws the specified text string at the specified location with the specified SolidBrush and Font objects using the formatting attributes of the specified <see cref="T:DevExpress.Pdf.PdfStringFormat"/>.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="point">A <see cref="T:System.Drawing.PointF"/> structure that specifies the upper-left corner of the drawn text.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> that specifies formatting attributes, such as line spacing and alignment, that are applied to the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Drawing.RectangleF)">
      <summary>
        <para>A set of overloaded methods used to draw the text string with the specified SolidBrush, Font objects and other parameters.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="layout">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the location of the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Drawing.RectangleF,DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Draws the specified text at the specified rectangle with the specified SolidBrush and Font objects using the formatting attributes of the specified PdfStringFormat.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="layout">A <see cref="T:System.Drawing.RectangleF"/> structure that specifies the location of the drawn text.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> that specifies formatting attributes, such as line spacing and alignment, that are applied to the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Single,System.Single)">
      <summary>
        <para>A set of overloaded methods used to draw the text string with the specified SolidBrush, Font objects and other parameters.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="x">A <see cref="T:System.Single"/> object that represents the x-coordinate of the upper-left corner of the drawn text.</param>
      <param name="y">A <see cref="T:System.Single"/> object that represents the y-coordinate of the upper-left corner of the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.DrawString(System.String,System.Drawing.Font,System.Drawing.SolidBrush,System.Single,System.Single,DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Draws the specified text string at the specified location with the specified SolidBrush and Font objects using the formatting attributes of the specified <see cref="T:DevExpress.Pdf.PdfStringFormat"/>.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> object to draw.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="brush">A <see cref="T:System.Drawing.SolidBrush"/> object that determines the color and texture of the drawn text.</param>
      <param name="x">A <see cref="T:System.Single"/> object that represents the x-coordinate of the upper-left corner of the drawn text.</param>
      <param name="y">A <see cref="T:System.Single"/> object that represents the y-coordinate of the upper-left corner of the drawn text.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> that specifies formatting attributes, such as line spacing and alignment, that are applied to the drawn text.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.FillEllipse(System.Drawing.Brush,System.Drawing.RectangleF)">
      <summary>
        <para>Fills the interior of an ellipse defined by a bounding rectangle specified by a RectangleF structure.</para>
      </summary>
      <param name="brush">A <see cref="T:System.Drawing.Brush"/> object that represents the brush used to fill the ellipse.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> structure that represents the bounding rectangle that defines the ellipse.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.FillPath(System.Drawing.Brush,System.Drawing.Drawing2D.GraphicsPath)">
      <summary>
        <para>Fills the interior of a GraphicsPath.</para>
      </summary>
      <param name="brush">A <see cref="T:System.Drawing.Brush"/> that determines the characteristics of the fill.</param>
      <param name="path">A <see cref="T:System.Drawing.Drawing2D.GraphicsPath"/> that represents the path to fill.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.FillPolygon(System.Drawing.Brush,System.Drawing.PointF[])">
      <summary>
        <para>Fills the interior of a polygon defined by an array of points specified by PointF structures.</para>
      </summary>
      <param name="brush">A <see cref="T:System.Drawing.Brush"/> object which represents the brush used to fill the polygon.</param>
      <param name="points">Array of <see cref="T:System.Drawing.PointF"/> structures that represent the vertices of the polygon to fill.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.FillRectangle(System.Drawing.Brush,System.Drawing.RectangleF)">
      <summary>
        <para>Fills the interior of a rectangle specified by a RectangleF structure.</para>
      </summary>
      <param name="brush">A <see cref="T:System.Drawing.Brush"/> object which represents the brush used to fill the rectangle.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> structure representing the rectangle bounds to fill.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.IntersectClip(System.Drawing.RectangleF)">
      <summary>
        <para>Updates the clip region of this <see cref="T:DevExpress.Pdf.PdfGraphics"/> object to the intersection of the current clip region and the specified RectangleF structure.</para>
      </summary>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> structure to intersect with the current clip region.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphics.JpegImageQuality">
      <summary>
        <para>Gets or sets the quality of Jpeg images in the resulting PDF file.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfGraphicsJpegImageQuality"/> enumeration value which specifies the quality of images in the resulting PDF file.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font and formatted with the specified <see cref="T:DevExpress.Pdf.PdfStringFormat"/>.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object that represents formatting information, such as line spacing, for the string.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,DevExpress.Pdf.PdfStringFormat,System.Single,System.Single)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font, horizontal and vertical resolutions and format.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object that represents formatting information, such as line spacing, for the string.</param>
      <param name="dpiX">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the horizontal resolution.</param>
      <param name="dpiY">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the vertical resolution.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,System.Drawing.SizeF)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font and text layout size.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="layoutSize">A <see cref="T:System.Drawing.SizeF"/> structure that specifies the maximum layout area for the text.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,System.Drawing.SizeF,DevExpress.Pdf.PdfStringFormat)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font, text layout size, and format.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="layoutSize">A <see cref="T:System.Drawing.SizeF"/> structure that specifies the maximum layout area for the text.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object that represents formatting information, such as line spacing, for the string.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,System.Drawing.SizeF,DevExpress.Pdf.PdfStringFormat,System.Single,System.Single)">
      <summary>
        <para>Measures the specified string when drawn with the specified Font, text layout size, horizontal and vertical resolutions, and format.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> to measure.</param>
      <param name="font">A <see cref="T:System.Drawing.Font"/> object that defines the text format of the string.</param>
      <param name="layoutSize">A <see cref="T:System.Drawing.SizeF"/> structure that specifies the maximum layout area for the text.</param>
      <param name="format">A <see cref="T:DevExpress.Pdf.PdfStringFormat"/> object that represents formatting information, such as line spacing, for the string.</param>
      <param name="dpiX">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the horizontal resolution.</param>
      <param name="dpiY">A <see cref="T:System.Single"/> object that represents the value, in dots per inch, for the vertical resolution.</param>
      <returns>A <see cref="T:System.Drawing.SizeF"/> structure that represents the measured size of the string.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.MeasureString(System.String,System.Drawing.Font,System.Drawing.SizeF,DevExpress.Pdf.PdfStringFormat,System.Single,System.Single,System.Int32@,System.Int32@)">
      <summary>
        <para>Measures the string when it is drawn with the specific font and formatted with the given string format.</para>
      </summary>
      <param name="text">A string to measure.</param>
      <param name="font">The font that defines the string&#39;s text format.</param>
      <param name="layoutSize">Specifies the maximum layout area for the text.</param>
      <param name="format">Provides formatting information (for example, alignment).</param>
      <param name="dpiX">The horizontal resolution (in dots per inch).</param>
      <param name="dpiY">The vertical resolution (in dots per inch).</param>
      <param name="charactersFitted">The number of characters in the string.</param>
      <param name="linesFilled">The number of lines in the string.</param>
      <returns>The string&#39;s measured size.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.RestoreGraphicsState">
      <summary>
        <para>Restores the most recently saved graphics state.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.RotateTransform(System.Single)">
      <summary>
        <para>Rotates the coordinate system  clockwise relative to its origin to the specified angle.</para>
      </summary>
      <param name="degree">Angle of rotation in degrees.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.SaveGraphicsState">
      <summary>
        <para>Saves the current graphics state to the stack.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.ScaleTransform(System.Single,System.Single)">
      <summary>
        <para>Scales the coordinate system according to the specified scale factor.</para>
      </summary>
      <param name="sx">Scale factor in the x direction.</param>
      <param name="sy">Scale factor in the y direction.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphics.TextOrigin">
      <summary>
        <para>Specifies how to interpret a point passed to one of the PdfGraphics.DrawString overload methods that take a PointF object.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfGraphicsTextOrigin"/> enumeration value. The default value is TopLeftCorner.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphics.TranslateTransform(System.Single,System.Single)">
      <summary>
        <para>Translates the origin of the coordinate system to the specified point.</para>
      </summary>
      <param name="x">A single structure that is the x-coordinate of the translation.</param>
      <param name="y">A single structure that is the y-coordinate of the translation.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphics.UseKerning">
      <summary>
        <para>Gets or sets a value which indicates whether kerning is used when drawing characters.</para>
      </summary>
      <value>true if kerning is used when drawing characters; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance">
      <summary>
        <para>Provides appearance settings used to paint the border of an interactive form field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance.Color">
      <summary>
        <para>Specifies the color of an interactive form field border.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure specifying the border color.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance.Style">
      <summary>
        <para>Specifies the style of an interactive form field border.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Pdf.PdfAcroFormBorderStyle"/> enumeration values that represents the style of an interactive form field border.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance.Width">
      <summary>
        <para>Specifies the width of the interactive form field border.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value that is the width of the interactive form field border. The default value is 1.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField">
      <summary>
        <para>Represents a check box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField.#ctor(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField"/> class with the specified check box field name and a rectangle where the check box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a check box field.</param>
      <param name="rectangle">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a check box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField.ButtonStyle">
      <summary>
        <para>Specifies a button style which is applied to a check box field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormButtonStyle"/> enumeration value that determines the button style applied to a check box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField.ExportValue">
      <summary>
        <para>Specifies an export value of a check box field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents an export value of a check box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField.IsChecked">
      <summary>
        <para>Gets or sets whether a check box is checked.</para>
      </summary>
      <value>true, if the check box is checked; otherwise, false. Default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCheckBoxField.ShouldGeneratePressedAppearance">
      <summary>
        <para>Specifies whether to generate a down appearance that will appear in a check box when the mouse button is pressed within the check box area.</para>
      </summary>
      <value>true, if a down appearance is generated within the check box area; otherwise, false. Default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField">
      <summary>
        <para>Represents the base class for combo box and list box fields.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.AddValue(System.String)">
      <summary>
        <para>Adds an item to a combo box and list box using their display value.</para>
      </summary>
      <param name="displayValue">A <see cref="T:System.String"/> that is a value to be added to combo box and list box.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.AddValue(System.String,System.String)">
      <summary>
        <para>Adds an item to a combo box and list box using their display and export values.</para>
      </summary>
      <param name="displayValue">A <see cref="T:System.String"/> that is a value to be added to a combo box and list box.</param>
      <param name="exportValue">A <see cref="T:System.String"/> that is an export value.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.ClearSelection">
      <summary>
        <para>Clears the combo box or list box selection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.ClearValues">
      <summary>
        <para>Removes all values to be added to the list box and combo box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.SelectValue(System.String)">
      <summary>
        <para>Selects an item of a combo box and list box by their export value.</para>
      </summary>
      <param name="exportValue">A <see cref="T:System.String"/> value that represents the item&#39;s export value.</param>
      <returns>true, if the combo box or list box item was successfully selected; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormChoiceField.SetSelected(System.Int32,System.Boolean)">
      <summary>
        <para>Sets the specified item&#39;s selection state in combo box and list box fields.</para>
      </summary>
      <param name="index">The zero-based index of the item whose selection state will be changed.</param>
      <param name="value">true, to select the item; false to unselect the item.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField">
      <summary>
        <para>Represents a combo box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField.#ctor(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField"/> class with the specified combo box field name and a rectangle where the combo box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a combo box field.</param>
      <param name="rectangle">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a combo box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField.Editable">
      <summary>
        <para>Gets or sets a value that determines whether text within a text box of the combo box field can be edited.</para>
      </summary>
      <value>true, if text within a text box of the combo box field can be edited; otherwise, false. The default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField.ValueFormat">
      <summary>
        <para>Provides access to the field value format options.</para>
      </summary>
      <value>An object that allows you to specify the value format.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormCommonField">
      <summary>
        <para>Represents the base class for common field types such as text box, check box, list box, combo box and signature.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCommonField.Rectangle">
      <summary>
        <para>Specifies a rectangle inside which an interactive form field is located on a page.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.RectangleF"/> object that represents a field rectangle.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormCommonField.TextAlignment">
      <summary>
        <para>Specifies the horizontal alignment of the interactive form field&#39;s text.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormStringAlignment"/> enumeration value that specifies how the text is horizontally aligned within the interactive form field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormField">
      <summary>
        <para>Represents the base class for all interactive form fields.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Appearance">
      <summary>
        <para>Specifies the appearance settings for the interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance"/> object that specifies the appearance of the interactive form field.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormField.CreateComboBox(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Creates a combo box field using the field name and a rectangle where the combo box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a combo box field.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a combo box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormComboBoxField"/> object that represents a created combo box field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormField.CreateListBox(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Creates a list box field using the field name and a rectangle where the list box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a list box field.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a list box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField"/> object that represents a created list box field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormField.CreateRadioGroup(System.String)">
      <summary>
        <para>Creates a radio group field using the field name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a radio group field.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField"/> object that represents a created radio group field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormField.CreateSignature(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Creates a signature field using the field name and a rectangle where a signature field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a signature field.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a signature field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField"/> object that represents a created signature field.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormField.CreateTextBox(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Creates a text box field using the field name and a rectangle where a text box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a text box field.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a text box field is located on a page.</param>
      <returns>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField"/> object that represents a created text box field.</returns>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Name">
      <summary>
        <para>Specifies the interactive form field name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the field name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Print">
      <summary>
        <para>Specifies whether the current form field is printed.</para>
      </summary>
      <value>true, if the current form field is printed on the page, otherwise, false. The default value is true.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.ReadOnly">
      <summary>
        <para>Gets or sets whether the interactive form  field allows editing.</para>
      </summary>
      <value>true, if end-users cannot modify the form field&#39;s value; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Required">
      <summary>
        <para>Gets or sets the required status of the interactive form field.</para>
      </summary>
      <value>true, if the form field must have a value at the time it is exported by a submit-form action; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Rotation">
      <summary>
        <para>Specifies the rotation to apply to interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormFieldRotation"/> enumeration value that represents the degree by which a form field is rotated counterclockwise relative to the page.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.ToolTip">
      <summary>
        <para>Specifies the form field&#39;s tooltip text.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the text displayed within the form field&#39;s tooltip.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormField.Visible">
      <summary>
        <para>Specifies whether the form field is visible on the page.</para>
      </summary>
      <value>true, if the form field is visible; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance">
      <summary>
        <para>Provides appearance settings used to paint an interactive form field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.BackgroundColor">
      <summary>
        <para>Specifies the form field&#39;s background color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the interactive form field&#39;s background color.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.BorderAppearance">
      <summary>
        <para>Specifies the border appearance settings for an interactive form field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormBorderAppearance"/> object that specifies the border appearance for the interactive form field. The default value is null (a border is not displayed in a form field).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.FontFamily">
      <summary>
        <para>Specifies the font family name for an interactive form field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> that specifies the font family name.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.FontSize">
      <summary>
        <para>Specifies the size of the interactive form field&#39;s font.</para>
      </summary>
      <value>A <see cref="T:System.Double"/> value representing the font size for the interactive form field. The default value: 12.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.FontStyle">
      <summary>
        <para>Specifies the style of the form field font.</para>
      </summary>
      <value>A <see cref="T:System.Windows.FontStyle"/> value, representing the font style.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormFieldAppearance.ForeColor">
      <summary>
        <para>Specifies the foreground color of an interactive form field.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> structure specifying the foreground color of an interactive form field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField">
      <summary>
        <para>Represents a list box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField.#ctor(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField"/> class with the specified list box field name and a rectangle where the list box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a list box field.</param>
      <param name="rectangle">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a list box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField.MultiSelect">
      <summary>
        <para>Specifies whether multiple items can be selected simultaneously in the list box field.</para>
      </summary>
      <value>true, if multiple selections of list box items are allowed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormListBoxField.TopIndex">
      <summary>
        <para>Specifies the index of the first visible item in the list box field.</para>
      </summary>
      <value>An integer value that is the zero-based index of the first visible item in the list box. Default value is 0.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField">
      <summary>
        <para>Represents a radio group field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField"/> class with the specified radio group field name.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a radio group field.</param>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.AddButton(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Adds a radio button to the radio group field using the radio button name and a rectangle that specifies the location of this button on a PDF page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a radio button.</param>
      <param name="rect">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a radio button is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.ButtonStyle">
      <summary>
        <para>Gets or sets the style that defines the appearance of a radio button within a radio group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormButtonStyle"/> enumeration value that determines the style of a radio button within a radio group.</value>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.ClearButtons">
      <summary>
        <para>Removes all radio buttons from the radio group field.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.RadioButtonCount">
      <summary>
        <para>Gets the number of radio buttons in the radio group field.</para>
      </summary>
      <value>An integer value that is the number of radio buttons in the radio group field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.SelectedIndex">
      <summary>
        <para>Specifies index of the selected item in radio group field.</para>
      </summary>
      <value>An integer value, representing the zero-based index of the radio group&#39;s selected item.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormRadioGroupField.ShouldGeneratePressedAppearance">
      <summary>
        <para>Specifies whether to generate a down appearance that will appear when the mouse button is pressed within the radio button area.</para>
      </summary>
      <value>true, if a down appearance is generated within the radio button area; otherwise, false. Default value is true.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField">
      <summary>
        <para>Represents the signature field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField.#ctor(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField"/> class with the specified signature field name and a rectangle where the signature field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a signature field.</param>
      <param name="rectangle">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a signature field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField.ContentImage">
      <summary>
        <para>Specifies an image displayed in the signature form field.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Image"/> object that is an image displayed in the signature form field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField.LineAlignment">
      <summary>
        <para>Specifies the vertical alignment of the string within the signature field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormStringAlignment"/> enumeration value that represents the vertical line alignment.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField.StretchContentImage">
      <summary>
        <para>Specifies whether an image should be stretched to fill the rectangle of a signature field.</para>
      </summary>
      <value>true, if an image is stretched to fill the rectangle of a signature field; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormSignatureField.Text">
      <summary>
        <para>Specifies text displayed in a signature field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents the text displayed in the signature field.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField">
      <summary>
        <para>Represents a text box field.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.#ctor(System.String,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField"/> class with the specified text box field name and a rectangle where the text box field will be located on a page.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> that specifies the name of a text box field.</param>
      <param name="rectangle">A <see cref="T:System.Drawing.RectangleF"/> object that represents a rectangle inside which a text box field is located on a page.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.MaxLength">
      <summary>
        <para>Specifies the maximum text length for a text box field.</para>
      </summary>
      <value>A positive integer value specifying the maximum number of characters allowed in a text box field or 0 indicating that the number of characters in a text box field is not limited.The default value is 0.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.Multiline">
      <summary>
        <para>Specifies whether the text box field can contain multiple lines of text.</para>
      </summary>
      <value>true, if the text box field can contain multiple lines of text; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.Scrollable">
      <summary>
        <para>Specifies whether a text box field can be scrollable when the entered text exceeds the field area.</para>
      </summary>
      <value>true, if a text box field is scrollable when the entered text exceeds the field area; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.SpellCheck">
      <summary>
        <para>Specifies whether text entered to a text box field is spell checked.</para>
      </summary>
      <value>true, if  text entered to a text box field is spell checked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.Text">
      <summary>
        <para>Specifies text of the text box field.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value which represents the text displayed in the text box field.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.Type">
      <summary>
        <para>Specifies the type of the text box field.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfAcroFormTextFieldType"/> enumeration value that represents the text box field type.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfGraphicsAcroFormTextBoxField.ValueFormat">
      <summary>
        <para>Provides access to the field value format options.</para>
      </summary>
      <value>An object that allows you to specify the value format.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfGraphicsJpegImageQuality">
      <summary>
        <para>Specifies how images are exported to PDF format.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsJpegImageQuality.High">
      <summary>
        <para>The resulting Jpeg image quality is high.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsJpegImageQuality.Highest">
      <summary>
        <para>The resulting Jpeg image quality is the highest.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsJpegImageQuality.Low">
      <summary>
        <para>The resulting Jpeg image quality is low.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsJpegImageQuality.Lowest">
      <summary>
        <para>The resulting Jpeg image quality is the lowest.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfGraphicsJpegImageQuality.Medium">
      <summary>
        <para>The resulting Jpeg image quality is medium.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfPrinterSettings">
      <summary>
        <para>Provides the additional printer settings to a PDF Viewer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfPrinterSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfPrinterSettings"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfPrinterSettings.#ctor(System.Drawing.Printing.PrinterSettings)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Pdf.PdfPrinterSettings"/> class with the specified settings.</para>
      </summary>
      <param name="settings">A <see cref="T:System.Drawing.Printing.PrinterSettings"/> object.</param>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.EnableLegacyPrinting">
      <summary>
        <para>Gets or sets a value which indicates whether legacy printing is enabled.</para>
      </summary>
      <value>true if a document is printed using the old printing engine; otherwise - false. The default value is false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.PageNumbers">
      <summary>
        <para>Specifies the PDF document page numbers to be printed.</para>
      </summary>
      <value>An array of integer values that correspond to page numbers.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.PageOrientation">
      <summary>
        <para>Specifies the orientation of pages to be printed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPrintPageOrientation"/> value. The default value is <see cref="F:DevExpress.Pdf.PdfPrintPageOrientation.Auto"/>.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.PrintingDpi">
      <summary>
        <para>Specifies the dpi value used to print the PDF document.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value which represents the printing dpi.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.PrintInGrayscale">
      <summary>
        <para>Gets or sets a value which indicates whether to print the document content in grayscale.</para>
      </summary>
      <value>true to print a document content in grayscale; false the current printer settings are used.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.PrintStickyNotes">
      <summary>
        <para>Gets or sets whether to print text markup annotations (sticky notes).</para>
      </summary>
      <value>true to print sticky notes; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.Scale">
      <summary>
        <para>Specifies the percentage scale factor of the document page to be printed.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value which represents the scale factor of the document page (the scale is measured as a percentage).</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.ScaleMode">
      <summary>
        <para>Specifies the page scale mode when a document is printing.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Pdf.PdfPrintScaleMode"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrinterSettings.Settings">
      <summary>
        <para>Provides access to the standard .NET Framework printer settings.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Printing.PrinterSettings"/> object containing the standard .NET Framework printer settings.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPrintPageEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PrintPage"/> and <see cref="E:DevExpress.Pdf.PdfDocumentProcessor.PrintPage"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrintPageEventArgs.PageCount">
      <summary>
        <para>Returns the total number of pages which were sent to the printer.</para>
      </summary>
      <value>An integer value which represents the total number of pages which were sent to the printer.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfPrintPageEventArgs.PageNumber">
      <summary>
        <para>Returns the page number of the currently printed page.</para>
      </summary>
      <value>An integer value that is the page number.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfPrintPageEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.PrintPage"/> and <see cref="E:DevExpress.Pdf.PdfDocumentProcessor.PrintPage"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies either the <see cref="T:DevExpress.Pdf.PdfDocumentProcessor"/> or <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Pdf.PdfPrintPageEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfPrintPageOrientation">
      <summary>
        <para>Lists the available document orientation modes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintPageOrientation.Auto">
      <summary>
        <para>The orientation is defined automatically to fit the page content to the specific paper type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintPageOrientation.Landscape">
      <summary>
        <para>Orientation of the document pages is landscape.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintPageOrientation.Portrait">
      <summary>
        <para>Orientation of the document pages is portrait.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfPrintScaleMode">
      <summary>
        <para>Lists the available document scale modes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintScaleMode.ActualSize">
      <summary>
        <para>A printed page is not scaled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintScaleMode.CustomScale">
      <summary>
        <para>A printed page is scaled by a specified percentage scale factor.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfPrintScaleMode.Fit">
      <summary>
        <para>A printed page is scaled to fit a specific paper size.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfQueryPageSettingsEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.QueryPageSettings"/>  and <see cref="E:DevExpress.Pdf.PdfDocumentProcessor.QueryPageSettings"/> events.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Pdf.PdfQueryPageSettingsEventArgs.PageNumber">
      <summary>
        <para>Gets the page number in a document.</para>
      </summary>
      <value>An integer value, specifying the page number.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfQueryPageSettingsEventArgs.PageSize">
      <summary>
        <para>Gets the size of the current page.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.SizeF"/> value.</value>
    </member>
    <member name="P:DevExpress.Pdf.PdfQueryPageSettingsEventArgs.PrintInGrayscale">
      <summary>
        <para>Gets or sets a value which indicates whether to print the document content in grayscale.</para>
      </summary>
      <value>true to print a document content in grayscale; false the current printer settings are used.</value>
    </member>
    <member name="T:DevExpress.Pdf.PdfQueryPageSettingsEventHandler">
      <summary>
        <para>A method that will handle the <see cref="E:DevExpress.Pdf.PdfDocumentProcessor.QueryPageSettings"/> and <see cref="E:DevExpress.XtraPdfViewer.PdfViewer.QueryPageSettings"/> events.</para>
      </summary>
      <param name="sender">The event source. This parameter identifies either the <see cref="T:DevExpress.Pdf.PdfDocumentProcessor"/> or <see cref="T:DevExpress.XtraPdfViewer.PdfViewer"/> which raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Pdf.PdfQueryPageSettingsEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Pdf.PdfRenderingEngine">
      <summary>
        <para>Lists values used to specify the rendering engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingEngine.DirectX">
      <summary>
        <para>DirectX rendering engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingEngine.GdiPlus">
      <summary>
        <para>GDI/GDI+ rendering engine.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Pdf.PdfRenderingEngine.Skia">
      <summary>
        <para>Skia graphics engine.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions">
      <summary>
        <para>Defines extension methods for the IPdfViewerAnnotationBuilder interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions.AsMarkupAnnotationBuilder(DevExpress.Pdf.IPdfViewerAnnotationBuilder)">
      <summary>
        <para>Casts the IPdfViewerAnnotationBuilder object to the IPdfViewerMarkupAnnotationBuilder object.</para>
      </summary>
      <param name="builder">Current IPdfViewerAnnotationBuilder object.</param>
      <returns>The object that contain information about the markup annotation.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions.AsTextAnnotationBuilder(DevExpress.Pdf.IPdfViewerAnnotationBuilder)">
      <summary>
        <para>Casts the IPdfViewerAnnotationBuilder object to the IPdfViewerTextAnnotationBuilder object.</para>
      </summary>
      <param name="builder">Current IPdfViewerAnnotationBuilder object.</param>
      <returns>The IPdfViewerTextAnnotationBuilder object that contains text annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions.AsTextAnnotationBuilder(DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder)">
      <summary>
        <para>Casts the IPdfViewerMarkupAnnotationBuilder object to the IPdfViewerTextAnnotationBuilder object.</para>
      </summary>
      <param name="builder">Current IPdfViewerMarkupAnnotationBuilder object.</param>
      <returns>The IPdfViewerTextAnnotationBuilder object that contains text annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions.AsTextMarkupAnnotationBuilder(DevExpress.Pdf.IPdfViewerAnnotationBuilder)">
      <summary>
        <para>Casts the IPdfViewerAnnotationBuilder object to the IPdfViewerTextMarkupAnnotationBuilder object.</para>
      </summary>
      <param name="builder">Current IPdfViewerAnnotationBuilder object.</param>
      <returns>The IPdfViewerTextMarkupAnnotationBuilder object that contains text markup annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationBuilderExtensions.AsTextMarkupAnnotationBuilder(DevExpress.Pdf.IPdfViewerMarkupAnnotationBuilder)">
      <summary>
        <para>Casts the IPdfViewerMarkupAnnotationBuilder object to the IPdfViewerTextMarkupAnnotationBuilder object.</para>
      </summary>
      <param name="builder">Current IPdfViewerMarkupAnnotationBuilder object.</param>
      <returns>The IPdfViewerTextMarkupAnnotationBuilder object that contains text markup annotation parameters.</returns>
    </member>
    <member name="T:DevExpress.Pdf.PdfViewerAnnotationExtensions">
      <summary>
        <para>Defines extension methods for the IPdfViewerAnnotation interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationExtensions.AsMarkupAnnotation(DevExpress.Pdf.IPdfViewerAnnotation)">
      <summary>
        <para>Casts the IPdfViewerAnnotation object to the IPdfViewerMarkupAnnotation object</para>
      </summary>
      <param name="annotation">Current IPdfViewerAnnotation object.</param>
      <returns>The IPdfViewerMarkupAnnotation object that contains markup annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationExtensions.AsTextAnnotation(DevExpress.Pdf.IPdfViewerAnnotation)">
      <summary>
        <para>Casts the IPdfViewerAnnotation object to the IPdfViewerTextAnnotation object.</para>
      </summary>
      <param name="annotation">Current IPdfViewerAnnotation instance.</param>
      <returns>The IPdfViewerTextAnnotation object that contains text annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationExtensions.AsTextAnnotation(DevExpress.Pdf.IPdfViewerMarkupAnnotation)">
      <summary>
        <para>Casts the IPdfViewerMarkupAnnotation object to the IPdfViewerTextAnnotation object.</para>
      </summary>
      <param name="annotation">Current IPdfViewerMarkupAnnotation instance.</param>
      <returns>The IPdfViewerTextAnnotation object that contains text annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationExtensions.AsTextMarkupAnnotation(DevExpress.Pdf.IPdfViewerAnnotation)">
      <summary>
        <para>Casts the IPdfViewerAnnotation object to the IPdfViewerTextMarkupAnnotation object.</para>
      </summary>
      <param name="annotation">Current IPdfViewerAnnotation object.</param>
      <returns>The IPdfViewerTextMarkupAnnotation object that contains text markup annotation parameters.</returns>
    </member>
    <member name="M:DevExpress.Pdf.PdfViewerAnnotationExtensions.AsTextMarkupAnnotation(DevExpress.Pdf.IPdfViewerMarkupAnnotation)">
      <summary>
        <para>Casts the IPdfViewerMarkupAnnotation object to the IPdfViewerTextMarkupAnnotation object.</para>
      </summary>
      <param name="annotation">Current IPdfViewerMarkupAnnotation instance.</param>
      <returns>The IPdfViewerTextMarkupAnnotation object that contains text markup annotation parameters.</returns>
    </member>
  </members>
</doc>