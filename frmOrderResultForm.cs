﻿using DevExpress.Export;
using DevExpress.LookAndFeel;
using DevExpress.XtraPrinting;
using DevExpress.XtraSplashScreen;
using HIH.Framework.AutoCrawingManager.Result;
using HIHWCFServiceAPP;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.Framework.AutoCrawingManager
{
    public partial class frmOrderResultForm : HIH.Framework.BaseUIDX.BaseBarEditForm
    {
        Image buttonImage;
        Image hotButtonImage;

        OverlayTextPainter overlayLabel;
        OverlayImagePainter overlayButton;
        public frmOrderResultForm()
        {
            InitializeComponent();
            this.buttonImage = IImageHelper.CreateImage(AutoCrawingManager.Properties.Resources.cancel_normal);
            this.hotButtonImage = IImageHelper.CreateImage(Properties.Resources.cancel_active);
            this.overlayLabel = new OverlayTextPainter();
            this.overlayButton = new OverlayImagePainter(buttonImage, hotButtonImage, OnCancelButtonClick);

            this.bar1.LinksPersistInfo.Clear();
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
                new DevExpress.XtraBars.LinkPersistInfo(this.doUpdateAllBtn),
                new DevExpress.XtraBars.LinkPersistInfo(this.ExportBtn),
                new DevExpress.XtraBars.LinkPersistInfo(this.btnExit,true)
            });

            isLoadPerm = true;

            MaximizeBox = true;

            this.gc.DataSource = list;
        }
        private enum BWSTATUS
        {
            START,
            CANCEL,
            RUNNING,
            END
        }
        private readonly string orderChangeTableName = "[uswms].[dbo].[订单变更表]";

        private readonly string orderTableName = "[uswms].[dbo].[订单表]";

        private readonly string containerTableName = "[uswms].[dbo].[船公司集装箱明细爬取]";

        private DataTable searchOrderDT = new DataTable();
        private DataTable searchContainerDT = new DataTable();
        IOverlaySplashScreenHandle waitHandle = null;

        IOverlaySplashScreenHandle ShowProgressPanel()
        {
            return SplashScreenManager.ShowOverlayForm(this);
        }

        IOverlaySplashScreenHandle ShowProgressPanelOnText()
        {
            return SplashScreenManager
                .ShowOverlayForm(this, customPainter: new OverlayWindowCompositePainter(overlayLabel, overlayButton));
        }

        void CloseProgressPanel(IOverlaySplashScreenHandle handle)
        {
            if (handle != null)
                SplashScreenManager.CloseOverlayForm(handle);
        }

        private BindingList<ISearchItem> list = new BindingList<ISearchItem>();



        private void frmOrderCompForm_Load(object sender, EventArgs e)
        {
            try
            {
                BindingList<ISearchItem> searList = (BindingList<ISearchItem>)vInParam;
                if (searList != null)
                {
                    foreach (ISearchItem sitem in searList)
                    {
                        sitem.Option = false;
                        list.Add(sitem);
                    }
                    this.countLabel.Text = "一共" + this.list.Count + "条";
                }


            }
            catch(Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
        //取消识别
        private void OnCancelButtonClick()
        {
            this.bworker.CancelAsync();
        }

        //通知开启
        private void ReportOnStart()
        {
            this.bworker.ReportProgress(0, BWSTATUS.START);
        }
        //通知开启
        private void ReportOnCanceling()
        {
            this.bworker.ReportProgress(0, BWSTATUS.CANCEL);
        }

        //通知进度
        private void ReportProgress(int per)
        {
            this.bworker.ReportProgress(per, BWSTATUS.RUNNING);
        }
        //通知结束
        private void ReportOnEnd()
        {
            this.bworker.ReportProgress(0, BWSTATUS.END);
        }

        //反写订单信息
        private void UpOrderInfo(string orderId, ISearchItem item,string lastTime)
        {
            try
            {
                if (string.IsNullOrEmpty(orderId))
                    throw new Exception("订单编号不可为空");

                this.searchOrderDT.Clear();
                this.searchOrderDT = GetDataList(this.orderTableName, new List<SearchInfo>() {
                    new SearchInfo("订单号",orderId,SqlOperator.Equal)
                });

                if (this.searchOrderDT.Rows.Count != 1)
                    throw new Exception("未找到该订单【" + orderId + "】或存在重复的订单");

                if (item == null )
                    throw new Exception("未抓取到有效数据");

                string catchDcEta = "";
                if(DateTime.TryParse(item.SeaETA,out DateTime etaResult))
                {
                    catchDcEta = etaResult.ToString("yyyy-MM-dd");
                }


                List<string> errorList = new List<string>();
                if (item.ShowDetail.Contains(">"))
                    errorList.Add("大船ETA变更,"+ item.ShowDetail);

                Dictionary<string, string> dic = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(catchDcEta))
                {
                    dic.Add("ETA", catchDcEta.Replace("无", ""));
                }
                dic.Add("船舶自动跟踪状态", string.Join("|", errorList));
                dic.Add("船舶状态更新时间", lastTime);

                Update(this.orderTableName, "订单号", orderId, dic);

                if (errorList.Count > 0)
                {
                    dic.Clear();
                    dic.Add("订单号", orderId);
                    dic.Add("用户名", ICF.ISO.UNAME);
                    dic.Add("用户姓名", ICF.ISO.URNAME);
                    dic.Add("修改时间", DateTime.Now.ToString());
                    dic.Add("修改内容", string.Join("|", errorList));
                    dic.Add("修改原因", "船舶自动跟踪状态");
                    dic.Add("更新字段", "ETA");
                    Insert(this.orderChangeTableName, dic);
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        //private void UpShipCompanyContainer(string orderId, ISearchItem item, string lastTime)
        //{
        //    try
        //    {
        //        if (string.IsNullOrEmpty(orderId))
        //            throw new Exception("订单编号不可为空");

        //        if (item == null || item.ContainerList == null)
        //            return;
        //        Dictionary<string, string> upDic = new Dictionary<string, string>();
        //        foreach (IResultContainer containerItem in item.ContainerList)
        //        {

        //            if (!string.IsNullOrEmpty(containerItem.ContainerNo))
        //            {
        //                this.searchContainerDT.Clear();
        //                this.searchContainerDT = GetDataList(this.containerTableName, new List<SearchInfo>() {
        //                new SearchInfo("订单号",orderId,SqlOperator.Equal),
        //                new SearchInfo("集装箱号",containerItem.ContainerNo,SqlOperator.Equal),
        //            });

        //                if (this.searchContainerDT.Rows.Count > 0)
        //                {
        //                    string Id = this.searchContainerDT.Rows[0]["ID"].ToString();
        //                    upDic.Clear();
        //                    if (!string.IsNullOrEmpty(containerItem.ContainerPickupTime))
        //                    {
        //                        upDic.Add("提箱日期", containerItem.ContainerPickupTime);
        //                    }
        //                    if (!string.IsNullOrEmpty(containerItem.UnloadingTime))
        //                    {
        //                        upDic.Add("卸船日期", containerItem.UnloadingTime);
        //                    }
        //                    if (!string.IsNullOrEmpty(containerItem.ReturnTime))
        //                    {
        //                        upDic.Add("还箱日期", containerItem.ReturnTime);
        //                    }

        //                    if (upDic.Count > 0)
        //                    {

        //                        Update(this.containerTableName, "ID", Id, upDic);
        //                    }

        //                }
        //                else
        //                {
        //                    upDic.Clear();
        //                    upDic.Add("更新时间", lastTime);
        //                    upDic.Add("订单号", orderId);
        //                    upDic.Add("提单号", item.BLNo);
        //                    upDic.Add("集装箱号", containerItem.ContainerNo);

        //                    if (!string.IsNullOrEmpty(containerItem.ContainerPickupTime))
        //                    {
        //                        upDic.Add("提箱日期", containerItem.ContainerPickupTime);
        //                    }
        //                    if (!string.IsNullOrEmpty(containerItem.UnloadingTime))
        //                    {
        //                        upDic.Add("卸船日期", containerItem.UnloadingTime);
        //                    }
        //                    if (!string.IsNullOrEmpty(containerItem.ReturnTime))
        //                    {
        //                        upDic.Add("还箱日期", containerItem.ReturnTime);
        //                    }

        //                    Insert(this.containerTableName, upDic);

        //                }

        //            }

        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        throw;
        //    }
        //}

        private void bworker_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                this.ReportOnStart();
                try
                {
                    if (this.list.Count > 0)
                    {
                        int per = 100 / this.list.Count;
                        int count = 1;

                        for (int index = 0; index < this.list.Count; index++)
                        {
                            if (this.bworker.CancellationPending)
                            {
                                this.ReportOnCanceling();
                                break;
                            }

                            ISearchItem _item = this.list[index];

                            if (_item.Option)
                            {
                                try
                                {
                                    string lastTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                                    //更新集装箱
                                    //this.UpShipCompanyContainer(_item.OrderNumber, _item, lastTime);
                                    if (_item.Status == QUESTATUS.SUCCESS)
                                    {
                                        this.UpOrderInfo(_item.OrderNumber, _item, lastTime);

                                        //更新成功后
                                        _item.LastTime = lastTime;
                                        _item.PriETA = _item.SeaETA.Replace("无", "");
                                        _item.ShowDetail = _item.SeaETA.Replace("无", "");
                                        _item.Result = "已完成上传";
                                    }

                                }
                                catch (Exception exc)
                                {
                                    _item.Status = QUESTATUS.FAIL;
                                    _item.Result = exc.Message;
                                    continue;

                                }
                                finally
                                {
                                    this.ReportProgress(per * count);
                                    count++;
                                }

                            }
                        }

                    }


                }
                catch (Exception exc)
                {
                    ICF.ISD.ShowError(exc.Message);
                }
                finally
                {
                    this.ReportOnEnd();
                }



            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }


        private void bworker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            switch ((BWSTATUS)e.UserState)
            {
                case BWSTATUS.START:
                    this.waitHandle = this.ShowProgressPanelOnText();
                    overlayLabel.Text = "0%";
                    break;
                case BWSTATUS.CANCEL:
                    overlayLabel.Text = "Cancel...";
                    break;
                case BWSTATUS.RUNNING:
                    overlayLabel.Text = e.ProgressPercentage.ToString() + "%";
                    break;
                case BWSTATUS.END:
                    overlayLabel.Text = "Ending...";
                    CloseProgressPanel(waitHandle);
                    break;
            }
        }

        private void bworker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                waitHandle = ShowProgressPanel();
                //if (this.isSuccessAndRemove.Checked)
                //{
                //    if (this.list.Count > 0)
                //    {
                //        this.gc.DataSource = null;

                //        for (int i = this.list.Count - 1; i >= 0; i--)
                //        {
                //            if (this.list[i].Status == QUESTATUS.SUCCESS
                //                && this.list[i].StatusStr == "已完成上传")
                //            {
                //                this.list.RemoveAt(i);
                //            }
                //        }
                //        this.countLabel.Text = "一共" + this.list.Count + "条";
                //        this.gc.DataSource = this.list;
                //    }
                //}
                this.btnExit.Enabled = true;
                this.gc.RefreshDataSource();

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
            finally
            {
                CloseProgressPanel(waitHandle);
            }
        }

        private void gdv_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            try
            {
                if (e.RowHandle >= 0)
                {
                    ISearchItem searchItem = this.gdv.GetRow(e.RowHandle) as ISearchItem;
                    if (searchItem != null)
                    {
                        if (searchItem.Status == QUESTATUS.FAIL)
                        {
                            e.Appearance.ForeColor = Color.White;
                            e.Appearance.BackColor = DXSkinColors.FillColors.Danger;
                        }
                    }
                }
            }
            catch(Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void doUpdateAllBtn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

                try
                {
                    if (ICF.ISD.ShowYesNoAndTips("是否批量上传已选择的行?"))
                    {
                        int count = 0;
                        foreach (ISearchItem item in this.list)
                        {
                            if (item.Option)
                                count++;
                        }
                        if (count <= 0)
                            throw new Exception("未选择任何数据");
                        this.btnExit.Enabled = false;
                        this.bworker.RunWorkerAsync();
                    }
                }
                catch (Exception exc)
                {
                    ICF.ISD.ShowError(exc.Message);
                }

        }

        private void gdv_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            try
            {
                int index = e.RowHandle;
                string colName = e.Column.FieldName;

                if (index >= 0 && colName == "Option")
                {
                    ISearchItem item = (ISearchItem)this.gdv.GetRow(index);
                    item.Option = !item.Option;
                    this.DoRefresh();
                }
                if (index >= 0 && colName == "SeaETA")
                {
                    ISearchItem item = (ISearchItem)this.gdv.GetRow(index);
                    bool ios = ICF.ISD.SubFormShowModalWithReturn("HIH.Framework.AutoCrawingManager.dll", "HIH.Framework.AutoCrawingManager.frmETAEditForm", "ETA修改", item.SeaETA, out object vOutData);
                    if (ios)
                    {
                        item.SeaETA = vOutData.ToString();
                        item.ShowDetail = item.PriETA != item.SeaETA
                                                          ? item.PriETA + ">" + item.SeaETA
                                                          : item.PriETA;



                        item.Status = QUESTATUS.SUCCESS;
                        item.Result = "已完成爬取比对";
                        this.DoRefresh();
                    }

                 
                }


            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
        public override void DoRefresh()
        {
            try
            {
                this.gc.RefreshDataSource();

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void allChecked_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                waitHandle = this.ShowProgressPanel();
                foreach (ISearchItem searchItem in this.list)
                {
                    searchItem.Option = this.allChecked.Checked;
                }
                this.DoRefresh();

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
            finally
            {
                this.CloseProgressPanel(waitHandle);
            }
        }

        protected override void DoReturn()
        {
            try
            {

                vOutParam = this.list;
                this.DialogResult = DialogResult.OK;

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }



        private void ExportBtn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                string saveFileName = "";
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.DefaultExt = "xls";
                saveFileDialog.Filter = "Excel文件(*.xls)|*.xls|Excel文件(*.xlsx)|*.xlsx";
                saveFileDialog.RestoreDirectory = true;
                saveFileDialog.Title = "Excel文件保存路径";
                saveFileDialog.FileName = "ETA更新" + DateTime.Now.ToString("yyyy-MM").ToString();

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    saveFileName = saveFileDialog.FileName;

                    string FileExt = Path.GetExtension(saveFileName).ToLower();
                    if (FileExt == ".xlsx")
                    {
                        gdv.ExportToXlsx(saveFileName);
                    }
                    else if (FileExt == ".xls")
                    {
                        gdv.ExportToXls(saveFileName);
                    }


                }

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }


    }
}
