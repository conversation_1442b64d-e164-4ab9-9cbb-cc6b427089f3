﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager
{
    public class IProcess
    {
        public IProcess(string processName,string replaceText)
        {
            this.ProcessName = processName;
            this.ReplaceText = replaceText;
        }

        public string ProcessName { get; set; }
        public string ReplaceText { get; set; }

        public virtual List<IProcessItem> Run(string searchKey, string replaceText)
        {
            return new List<IProcessItem>();
        }



    }
}
