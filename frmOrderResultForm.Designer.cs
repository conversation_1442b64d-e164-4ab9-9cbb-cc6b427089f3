﻿
namespace HIH.Framework.AutoCrawingManager
{
    partial class frmOrderResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmOrderResultForm));
            this.gc = new DevExpress.XtraGrid.GridControl();
            this.gdv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.InOptCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LastLateStatusCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LastTimeCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.StatusCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.OrderNumberCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.BlNoCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.EtaCol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ShowETACol = new DevExpress.XtraGrid.Columns.GridColumn();
            this.bworker = new System.ComponentModel.BackgroundWorker();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.allChecked = new DevExpress.XtraEditors.CheckEdit();
            this.countLabel = new DevExpress.XtraEditors.LabelControl();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.doUpdateAllBtn = new DevExpress.XtraBars.BarButtonItem();
            this.ExportBtn = new DevExpress.XtraBars.BarButtonItem();
            ((System.ComponentModel.ISupportInitialize)(this.dt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.allChecked.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // btnAdd
            // 
            this.btnAdd.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnAdd.ImageOptions.SvgImage")));
            // 
            // btnSave
            // 
            this.btnSave.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnSave.ImageOptions.SvgImage")));
            // 
            // barMenu
            // 
            this.barMenu.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.doUpdateAllBtn,
            this.ExportBtn});
            this.barMenu.MaxItemId = 7;
            // 
            // bar1
            // 
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.doUpdateAllBtn),
            new DevExpress.XtraBars.LinkPersistInfo(this.ExportBtn)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            // 
            // btnRefresh
            // 
            this.btnRefresh.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnRefresh.ImageOptions.SvgImage")));
            // 
            // btnExit
            // 
            this.btnExit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnExit.ImageOptions.SvgImage")));
            // 
            // gc
            // 
            this.gc.Location = new System.Drawing.Point(12, 54);
            this.gc.MainView = this.gdv;
            this.gc.MenuManager = this.barMenu;
            this.gc.Name = "gc";
            this.gc.Size = new System.Drawing.Size(982, 468);
            this.gc.TabIndex = 4;
            this.gc.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gdv});
            // 
            // gdv
            // 
            this.gdv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.InOptCol,
            this.LastLateStatusCol,
            this.LastTimeCol,
            this.StatusCol,
            this.OrderNumberCol,
            this.BlNoCol,
            this.EtaCol,
            this.ShowETACol});
            this.gdv.GridControl = this.gc;
            this.gdv.Name = "gdv";
            this.gdv.OptionsBehavior.Editable = false;
            this.gdv.OptionsView.ShowGroupPanel = false;
            this.gdv.RowCellClick += new DevExpress.XtraGrid.Views.Grid.RowCellClickEventHandler(this.gdv_RowCellClick);
            this.gdv.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gdv_RowStyle);
            // 
            // InOptCol
            // 
            this.InOptCol.Caption = "操作";
            this.InOptCol.FieldName = "Option";
            this.InOptCol.Name = "InOptCol";
            this.InOptCol.OptionsColumn.AllowEdit = false;
            this.InOptCol.Visible = true;
            this.InOptCol.VisibleIndex = 0;
            // 
            // LastLateStatusCol
            // 
            this.LastLateStatusCol.Caption = "最近更新";
            this.LastLateStatusCol.FieldName = "LastTimeDesc";
            this.LastLateStatusCol.Name = "LastLateStatusCol";
            this.LastLateStatusCol.OptionsColumn.AllowEdit = false;
            this.LastLateStatusCol.Visible = true;
            this.LastLateStatusCol.VisibleIndex = 6;
            // 
            // LastTimeCol
            // 
            this.LastTimeCol.Caption = "最近更新时间";
            this.LastTimeCol.FieldName = "LastTime";
            this.LastTimeCol.Name = "LastTimeCol";
            this.LastTimeCol.OptionsColumn.AllowEdit = false;
            this.LastTimeCol.Visible = true;
            this.LastTimeCol.VisibleIndex = 2;
            // 
            // StatusCol
            // 
            this.StatusCol.Caption = "状态";
            this.StatusCol.FieldName = "Result";
            this.StatusCol.Name = "StatusCol";
            this.StatusCol.OptionsColumn.AllowEdit = false;
            this.StatusCol.Visible = true;
            this.StatusCol.VisibleIndex = 1;
            // 
            // OrderNumberCol
            // 
            this.OrderNumberCol.Caption = "订单号";
            this.OrderNumberCol.FieldName = "OrderNumber";
            this.OrderNumberCol.Name = "OrderNumberCol";
            this.OrderNumberCol.OptionsColumn.AllowEdit = false;
            this.OrderNumberCol.Visible = true;
            this.OrderNumberCol.VisibleIndex = 3;
            // 
            // BlNoCol
            // 
            this.BlNoCol.Caption = "提单号";
            this.BlNoCol.FieldName = "BLNo";
            this.BlNoCol.Name = "BlNoCol";
            this.BlNoCol.OptionsColumn.AllowEdit = false;
            this.BlNoCol.Visible = true;
            this.BlNoCol.VisibleIndex = 4;
            // 
            // EtaCol
            // 
            this.EtaCol.Caption = "ETA";
            this.EtaCol.FieldName = "SeaETA";
            this.EtaCol.Name = "EtaCol";
            this.EtaCol.Visible = true;
            this.EtaCol.VisibleIndex = 5;
            // 
            // ShowETACol
            // 
            this.ShowETACol.Caption = "变更";
            this.ShowETACol.FieldName = "ShowDetail";
            this.ShowETACol.Name = "ShowETACol";
            this.ShowETACol.OptionsColumn.AllowEdit = false;
            this.ShowETACol.Visible = true;
            this.ShowETACol.VisibleIndex = 7;
            // 
            // bworker
            // 
            this.bworker.WorkerReportsProgress = true;
            this.bworker.WorkerSupportsCancellation = true;
            this.bworker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bworker_DoWork);
            this.bworker.ProgressChanged += new System.ComponentModel.ProgressChangedEventHandler(this.bworker_ProgressChanged);
            this.bworker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bworker_RunWorkerCompleted);
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.allChecked);
            this.dataLayoutControl1.Controls.Add(this.countLabel);
            this.dataLayoutControl1.Controls.Add(this.gc);
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 24);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(1006, 534);
            this.dataLayoutControl1.TabIndex = 5;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // allChecked
            // 
            this.allChecked.Location = new System.Drawing.Point(12, 12);
            this.allChecked.MenuManager = this.barMenu;
            this.allChecked.Name = "allChecked";
            this.allChecked.Properties.Appearance.Options.UseTextOptions = true;
            this.allChecked.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.allChecked.Properties.Caption = "全选";
            this.allChecked.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.allChecked.Size = new System.Drawing.Size(982, 20);
            this.allChecked.StyleController = this.dataLayoutControl1;
            this.allChecked.TabIndex = 6;
            this.allChecked.CheckedChanged += new System.EventHandler(this.allChecked_CheckedChanged);
            // 
            // countLabel
            // 
            this.countLabel.Location = new System.Drawing.Point(951, 36);
            this.countLabel.Name = "countLabel";
            this.countLabel.Size = new System.Drawing.Size(43, 14);
            this.countLabel.StyleController = this.dataLayoutControl1;
            this.countLabel.TabIndex = 5;
            this.countLabel.Text = "一共0条";
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.layoutControlItem2,
            this.layoutControlItem3,
            this.emptySpaceItem1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(1006, 534);
            this.Root.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gc;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 42);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(986, 472);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.AppearanceItemCaption.Options.UseTextOptions = true;
            this.layoutControlItem2.AppearanceItemCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.layoutControlItem2.ContentHorzAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.layoutControlItem2.Control = this.countLabel;
            this.layoutControlItem2.Location = new System.Drawing.Point(939, 24);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(47, 18);
            this.layoutControlItem2.Text = "一共0条";
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.allChecked;
            this.layoutControlItem3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(986, 24);
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 24);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(939, 18);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // doUpdateAllBtn
            // 
            this.doUpdateAllBtn.Caption = "一键更新";
            this.doUpdateAllBtn.Id = 5;
            this.doUpdateAllBtn.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("doUpdateAllBtn.ImageOptions.Image")));
            this.doUpdateAllBtn.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("doUpdateAllBtn.ImageOptions.LargeImage")));
            this.doUpdateAllBtn.Name = "doUpdateAllBtn";
            this.doUpdateAllBtn.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.doUpdateAllBtn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.doUpdateAllBtn_ItemClick);
            // 
            // ExportBtn
            // 
            this.ExportBtn.Caption = "导出";
            this.ExportBtn.Id = 6;
            this.ExportBtn.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("ExportBtn.ImageOptions.Image")));
            this.ExportBtn.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("ExportBtn.ImageOptions.LargeImage")));
            this.ExportBtn.Name = "ExportBtn";
            this.ExportBtn.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.ExportBtn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.ExportBtn_ItemClick);
            // 
            // frmOrderResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1006, 558);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "frmOrderResultForm";
            this.Text = "frmOrderCompForm";
            this.Load += new System.EventHandler(this.frmOrderCompForm_Load);
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.allChecked.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gc;
        private DevExpress.XtraGrid.Views.Grid.GridView gdv;
        private DevExpress.XtraGrid.Columns.GridColumn InOptCol;
        private DevExpress.XtraGrid.Columns.GridColumn StatusCol;
        private DevExpress.XtraGrid.Columns.GridColumn LastLateStatusCol;
        private DevExpress.XtraGrid.Columns.GridColumn LastTimeCol;
        private DevExpress.XtraGrid.Columns.GridColumn OrderNumberCol;
        private DevExpress.XtraGrid.Columns.GridColumn BlNoCol;
        private DevExpress.XtraGrid.Columns.GridColumn EtaCol;
        private System.ComponentModel.BackgroundWorker bworker;
        private DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;
        private DevExpress.XtraEditors.LabelControl countLabel;
        private DevExpress.XtraLayout.LayoutControlGroup Root;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraBars.BarButtonItem doUpdateAllBtn;
        private DevExpress.XtraEditors.CheckEdit allChecked;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
        private DevExpress.XtraBars.BarButtonItem ExportBtn;
        private DevExpress.XtraGrid.Columns.GridColumn ShowETACol;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;
    }
}