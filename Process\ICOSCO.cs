using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class ICOSCO : IProcess
    {
        private readonly string SearchMainPage = "https://elines.coscoshipping.com/scct/public/ct/base?lang=zh&trackingType=BILLOFLADING&number=";

        public ICOSCO():base("COSCO","COSU"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(4, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage + searchKey;
            secPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }catch(Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch(Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                string trPattern = @"<tr[^>]*class=""ant-table-row ant-table-row-level-0""[^>]*>(.*?)</tr>";

                MatchCollection containerMatches = Regex.Matches(containerString, trPattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    string tdContainerString = containerMatch.Value;
                    string tdPattern = @"<td[^>]*class=""ant-table-cell[^>]*>(.*?)<\/td>";
                    MatchCollection tdMatches = Regex.Matches(tdContainerString, tdPattern, RegexOptions.Singleline);
                    string containerNo = "";
                    string returnDateString = "";
                    string pickupDateString = "";
                    string uploadDateString = "";
                    foreach (Match tdMatch in tdMatches)
                    {
                        string td = tdMatch.Groups[1].Value.Replace("<!---->", "");
                        if (td.Contains("div"))
                        {
                            string divPattern = @"<div[^>]*>(.*?)<\/div>";
                            MatchCollection divMatches = Regex.Matches(td, divPattern, RegexOptions.Singleline);
                            if (divMatches.Count == 1)
                            {
                                containerNo = divMatches[0].Groups[1].Value;
                            }
                            if (divMatches.Count == 2)
                            {
                                if (divMatches[0].Groups[1].Value == "还空箱"
                                    ||
                                   divMatches[0].Groups[1].Value == "Empty Return")
                                {
                                    returnDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }
                                if (divMatches[0].Groups[1].Value == "提取重箱"
                                    ||
                                   divMatches[0].Groups[1].Value == "Laden Pick up")
                                {
                                    pickupDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }

                                if (divMatches[0].Groups[1].Value == "目的港卸货"
                                    ||
                                   divMatches[0].Groups[1].Value.StartsWith("Discharged"))
                                {
                                    uploadDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }

                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(containerNo))
                    {
                        IResultContainer containerItem = new IResultContainer();
                        Console.WriteLine(containerNo + "-" + pickupDateString + "-" + returnDateString);
                        containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                        containerList.Add(containerItem);
                    }

                }
                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string GetETA(string result)
        {
            try
            {
                // 查找包含时间信息的行容器
                string rowPattern = @"<div[^>]*class=""ant-row css-1tiubaq""[^>]*>(.*?)</div>";
                MatchCollection rowMatches = Regex.Matches(result, rowPattern, RegexOptions.Singleline);

                if (rowMatches.Count <= 0)
                    throw new Exception("未找到时间信息行");

                List<string> estimatedArrivalDates = new List<string>();
                List<string> actualArrivalDates = new List<string>();

                foreach (Match rowMatch in rowMatches)
                {
                    string rowContent = rowMatch.Groups[1].Value;

                    // 查找列信息
                    string colPattern = @"<div[^>]*class=""ant-row css-1tiubaq""[^>]*>(.*?)</div>";
                    MatchCollection colMatches = Regex.Matches(rowContent, colPattern, RegexOptions.Singleline);

                    for (int i = 0; i < colMatches.Count; i++)
                    {
                        string colContent = colMatches[i].Groups[1].Value;

                        // 检查是否包含"预计到港"
                        if (colContent.Contains("预计到港"))
                        {
                            // 查找对应的时间信息
                            string datePattern = @"<span[^>]*class=""date""[^>]*>(.*?)</span>";
                            Match dateMatch = Regex.Match(colContent, datePattern, RegexOptions.Singleline);
                            if (dateMatch.Success)
                            {
                                string dateValue = dateMatch.Groups[1].Value.Trim();
                                if (!string.IsNullOrEmpty(dateValue) && DateTime.TryParse(dateValue, out DateTime parsedDate))
                                {
                                    estimatedArrivalDates.Add(dateValue);
                                }
                            }
                        }
                        // 检查是否包含"实际到港"
                        else if (colContent.Contains("实际到港"))
                        {
                            // 查找对应的时间信息
                            string datePattern = @"<span[^>]*class=""date""[^>]*>(.*?)</span>";
                            Match dateMatch = Regex.Match(colContent, datePattern, RegexOptions.Singleline);
                            if (dateMatch.Success)
                            {
                                string dateValue = dateMatch.Groups[1].Value.Trim();
                                if (!string.IsNullOrEmpty(dateValue) && DateTime.TryParse(dateValue, out DateTime parsedDate))
                                {
                                    actualArrivalDates.Add(dateValue);
                                }
                            }
                        }
                    }
                }

                // 优先返回最后一个预计到港时间
                if (estimatedArrivalDates.Count > 0)
                {
                    string lastEstimatedDate = estimatedArrivalDates.Last();
                    if (DateTime.TryParse(lastEstimatedDate, out DateTime etaDate))
                    {
                        return etaDate.ToString("yyyy-MM-dd");
                    }
                }

                // 如果没有预计到港，返回最后一个实际到港时间
                if (actualArrivalDates.Count > 0)
                {
                    string lastActualDate = actualArrivalDates.Last();
                    if (DateTime.TryParse(lastActualDate, out DateTime ataDate))
                    {
                        return ataDate.ToString("yyyy-MM-dd");
                    }
                }

                throw new Exception("未找到有效的到港时间信息");
            }
            catch (Exception exc)
            {
                throw;
            }
        }


    }
}
