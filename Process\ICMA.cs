﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class ICMA : IProcess
    {
        private readonly string SearchMainPage = "https://www.cma-cgm.com/";

        public ICMA():base("CMA","CMDU"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "展开", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = $"document.getElementById('track-number').value='{ searchKey }';console.log(document.getElementById('searchName').value);";
            thiPro.JScript = "document.getElementById('searchTracking').click();";
            fouPro.JScript = "const switches = document.querySelectorAll('input[id^=\"card-details-switch\"]');switches.forEach(switchElement => {switchElement.click(); switchElement.checked = true });";
            fifPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string result)
        {
            try
            {

                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();
                string pattern = @"<script>[^>]*options.responseData[^>]*=(.*?)<\/script>";
                MatchCollection matches = Regex.Matches(result, pattern, RegexOptions.Singleline);
                foreach (Match match in matches)
                {
                    string matchStr = match.Value;
                    var regex = new Regex(@"options\.responseData\s*=\s*'([^']+)';");
                    Match nextMatch = regex.Match(matchStr);

                    if (nextMatch.Success)
                    {

                        string containerNo = "";
                        string returnDateString = "";
                        string pickupDateString = "";
                        string uploadDateString = "";
                        string jsonStr = nextMatch.Groups[1].Value;
                        var obj = JObject.Parse(jsonStr);
                        containerNo = obj["ContainerReference"]?.ToString() ?? "";
                        if (obj["PastMoves"] != null)
                        {
                            foreach (JToken move in obj["PastMoves"])
                            {
                                if ((move["StatusDescription"]?.ToString() ?? "") == "Discharged")
                                {
                                    uploadDateString = this.TryConvertDate(move["DateString"]?.ToString() ?? "");
                                }
                                if ((move["StatusDescription"]?.ToString() ?? "") == "Container to consignee")
                                {
                                    pickupDateString = this.TryConvertDate(move["DateString"]?.ToString() ?? "");
                                }

                            }

                        }
                        if (obj["CurrentMoves"] != null)
                        {
                            foreach (JToken move in obj["CurrentMoves"])
                            {
                                if ((move["StatusDescription"]?.ToString() ?? "") == "Discharged")
                                {
                                    uploadDateString = this.TryConvertDate(move["DateString"]?.ToString() ?? "");
                                }
                                if ((move["StatusDescription"]?.ToString() ?? "") == "Container to consignee")
                                {
                                    pickupDateString = this.TryConvertDate(move["DateString"]?.ToString() ?? "");
                                }
                                if ((move["StatusDescription"]?.ToString() ?? "") == "Empty in depot")
                                {
                                    returnDateString = this.TryConvertDate(move["DateString"]?.ToString() ?? "");
                                }
                            }

                        }
                        if (!string.IsNullOrEmpty(containerNo))
                        {
                            IResultContainer containerItem = new IResultContainer();
                            containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                            containerList.Add(containerItem);
                        }
                    }
                }
                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string GetETA(string result)
        {
            try
            {

                result = result.Replace("\\u003C", "<").Replace("\\n", "").Replace("\\", "");

                string contentPattern = @"<div[^>]*class=""[^""]*main-wrapper""[^>]*>[^>]*<ul>(.*?)<\/ul>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string ulPattern = @"<ul>(.*?)<\/ul>";

                MatchCollection ulMatches = Regex.Matches(matchVal, ulPattern, RegexOptions.Singleline);

                if (ulMatches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = ulMatches[0].Value;

                string liPattern = @"<li[^>]*class=""[^""]*cardelem""[^>]*>(.*?)<\/li>";

                MatchCollection liMatches = Regex.Matches(matchVal, liPattern, RegexOptions.Singleline);

                if (liMatches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = liMatches[0].Value;

                string etaPattern = @"</div>[^>]*<span>(.*?)<\/span>[^>]*<span[^>]*class=""time"">";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("ETA为空");

                return this.ParseExactTime(etaValue);


            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string TryConvertDate(string input)
        {
            try
            {
                var culture = new CultureInfo("en-US");
                var date = DateTime.ParseExact(
                    input.Trim(), // 去除前后空格
                    new[] { "dddd,dd-MMM-yyyy", "dddd, d-MMM-yyyy", "dddd, dd-MMM-yyyy" },
                    culture,
                    DateTimeStyles.AllowWhiteSpaces
                );

                return date.ToString("yyyy-MM-dd");
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                string inputFormat = "ddd.dd-MMM-yyyy";
                DateTime date = DateTime.ParseExact(inputDate, inputFormat, CultureInfo.InvariantCulture);
                string outputFormat = "yyyy-MM-dd";
                return date.ToString(outputFormat);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

    }
}
