﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class ISSPH : IProcess
    {
        private readonly string SearchMainPage = "https://www.goldstarline.com/#/track_shipment";

        public ISSPH():base("GOLD STAR", ""){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = $@"
                            const input = document.getElementById('containerid');
                            if (input) {{
                                input.focus();
        
                                // 触发值变更（React专用处理）
                                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
                                    HTMLInputElement.prototype, 
                                    'value'
                                ).set;
                                nativeInputValueSetter.call(input, '{searchKey}');

                                // 生成带冒泡的事件
                                const inputEvent = new Event('input', {{
                                    bubbles: true,
                                    cancelable: true,
                                    composed: true
                                }});
                                input.dispatchEvent(inputEvent);

                                // 异步处理失焦事件
                                setTimeout(() => {{
                                    const blurEvent = new FocusEvent('blur', {{
                                        bubbles: true,
                                        relatedTarget: document.body
                                    }});
                                    input.dispatchEvent(blurEvent);
                                    document.body.click();
                                }}, 100);
                            }}
                        ";
            thiPro.JScript = "document.getElementById('submitDetails').click();";
            fouPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {

                result = result.Replace("\\u003C", "<").Replace("\\n", "").Replace("\\", "");

                string contentPattern = @"<div[^>]*class=""[^""]*trackShipmentResultRowInner"">(.*?)<hr>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string ulPattern = @"<div[^>]*class=""[^""]*cartItem"">(.*?)<\/div>";

                MatchCollection ulMatches = Regex.Matches(matchVal, ulPattern, RegexOptions.Singleline);

                if (ulMatches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string etaMatchValue = "";

                foreach(Match ulMatch in ulMatches)
                {
                    string ul = ulMatch?.Value??"";
                    if (ul.Contains("ETA"))
                    {
                        etaMatchValue = ul;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(etaMatchValue))
                    throw new Exception("未找到正确的匹配对象");


                string h3Pattern = @"<h3>(.*?)<\/h3>";
                MatchCollection h3Matches = Regex.Matches(etaMatchValue, h3Pattern, RegexOptions.Singleline);

                if (h3Matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");


                string etaValue = h3Matches[0].Groups[1].Value;

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("ETA为空");

                return this.ParseExactTime(etaValue);


            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private string ParseExactTime(string inputDate)
        {
            try
            {
                // 指定英文文化（确保正确解析月份缩写）
                CultureInfo provider = CultureInfo.CreateSpecificCulture("en-US");

                // 解析原始日期格式
                DateTime date = DateTime.ParseExact(
                    inputDate,
                    "dd-MMM-yyyy",
                    provider,
                    DateTimeStyles.None
                );

                return date.ToString("yyyy-MM-dd");

            }
            catch (Exception exc)
            {
                throw;
            }
        }

    }
}
