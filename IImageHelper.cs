﻿using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.Utils;
using DevExpress.Utils.Drawing;
using DevExpress.Utils.Svg;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager
{
    public static class IImageHelper
    {
        public static Image CreateImage(byte[] data, ISkinProvider skinProvider = null)
        {
            SvgBitmap svgBitmap = new SvgBitmap(data);
            return svgBitmap.Render(SvgPaletteHelper.GetSvgPalette(skinProvider ?? UserLookAndFeel.Default, ObjectState.Normal), ScaleUtils.GetScaleFactor().Height);
        }

        public static string ImageToBase64(Image _image)
        {
            string imageBase64 = "";
            try
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    _image.Save(ms, ImageFormat.Png);

                    byte[] arr = new byte[ms.Length];
                    ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length);
                    imageBase64 = Convert.ToBase64String(arr);
                }

            }
            catch
            {
                throw;
            }
            return imageBase64;
        }
    }

}
