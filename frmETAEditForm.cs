﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.Framework.AutoCrawingManager
{
    public partial class frmETAEditForm : HIH.Framework.BaseUIDX.BaseBarEditForm
    {
        public frmETAEditForm()
        {
            InitializeComponent();
            this.bar1.LinksPersistInfo.Clear();
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
                new DevExpress.XtraBars.LinkPersistInfo(this.btnSave),
                new DevExpress.XtraBars.LinkPersistInfo(this.btnExit,true)
            });
        }

        private void frmOrderETA_Load(object sender, EventArgs e)
        {
            try
            {
                string eta = (string)vInParam;
                if (DateTime.TryParse(eta,out DateTime dat))
                {
                    this.ETAEdit.Value = dat;
                }

            }catch(Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        public override void DoSave()
        {
            try
            {
                vOutParam = this.ETAEdit.Value.ToString("yyyy-MM-dd");
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

    }
}
