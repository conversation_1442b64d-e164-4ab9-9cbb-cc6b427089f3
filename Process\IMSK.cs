﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IMSK : IProcess
    {
        private readonly string SearchMainPage = "https://www.maersk.com.cn/tracking/";

        public IMSK():base("MSK","MAEU"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage + searchKey;
            secPro.JScript = @"
                        Array.from(document.getElementsByTagName('mc-button')).forEach(function(button) {
                            // 访问 Shadow DOM（需组件 mode: open）
                            if (button.shadowRoot) {
                                // 定位指定 part 属性的 slot
                                var slot = button.shadowRoot.querySelector('slot[part=""text-and-icon-label""]');
            
                                // 检查文本内容（使用 trim 清理空白）
                                if (slot && slot.innerText.trim() === 'Show details') {
                                    // 触发点击事件
                                    button.click();
                                    // 可选：添加点击标记防止重复点击
                                    button._clickProcessed = true;
                                }
                            }
                        });
                    ";
            thiPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                string containerPattern = @"<div[^>]*data-test=""container""[^>]*>(.*?)<\/div>";
                MatchCollection containerMatches = Regex.Matches(containerString, containerPattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    string containerNo = "";
                    string returnDateString = "";
                    string pickupDateString = "";
                    string uploadingDateString = "";
                    string spanPattern = @"<span[^>]*class=""mds-text--medium-bold""[^>]*>(.*?)<\/span>";
                    MatchCollection spanMatches = Regex.Matches(containerMatch.Value, spanPattern, RegexOptions.Singleline);
                    if (spanMatches.Count <= 0) continue;
                    containerNo = spanMatches[0].Groups[1].Value;

                    string ulPattern = @"<ul[^>]*class=""transport-plan__list""[^>]*>(.*?)<\/ul>";
                    MatchCollection ulMatches = Regex.Matches(containerString, ulPattern, RegexOptions.Singleline);

                    if (ulMatches.Count <= 0) continue;

                    foreach (Match ulMatch in ulMatches)
                    {
                        string ulString = ulMatch.Value;
                        string liPattern = @"<li[^>]*data-test=""transport-plan-item-complete""[^>]*>(.*?)<\/li>";
                        MatchCollection liMatches = Regex.Matches(ulString, liPattern, RegexOptions.Singleline);
                        string desc = "";
                        string date = "";
                        foreach (Match liMatch in liMatches)
                        {
                            string descPattern = @"<div[^>]*data-test=""milestone""[^>]*>[^>]*<span>(.*?)<\/span>";
                            MatchCollection descMatches = Regex.Matches(liMatch.Value, descPattern, RegexOptions.Singleline);
                            if (descMatches.Count > 0)
                            {
                                desc = descMatches[0].Groups[1].Value;
                            }
                            string datePattern = @"<span[^>]*data-test=""milestone-date""[^>]*>(.*?)<\/span>";
                            MatchCollection dateMatches = Regex.Matches(liMatch.Value, datePattern, RegexOptions.Singleline);
                            if (dateMatches.Count > 0)
                            {
                                date = this.ParseExactTime(dateMatches[0].Groups[1].Value);
                            }

                            if (desc == "Discharge" || desc == "卸货")
                            {
                                uploadingDateString = date;
                            }
                            if (desc == "Gate out for delivery" || desc == "出港待送货")
                            {
                                pickupDateString = date;
                            }
                            if (desc == "Empty container return" || desc == "空集装箱归还")
                            {
                                returnDateString = date;
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(containerNo))
                    {
                        IResultContainer containerItem = new IResultContainer();
                        Console.WriteLine(containerNo + "-" + pickupDateString + "-" + uploadingDateString + "-" + returnDateString);
                        containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadingDateString, returnDateString);
                        containerList.Add(containerItem);
                    }
                }


                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }
        
        private string GetETA(string result)
        {
            try
            {

                string contentPattern = @"<div[^>]*data-test=""[^""]*milestone[^""]*""[^>]*>(.*?)<\/div>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0 || matches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = "";
                foreach (Match match in matches)
                {
                    if (match.Value.ToLower().Contains("vessel arrival") || match.Value.ToLower().Contains("船舶到港"))
                    {
                        string etaPattern = @"<span[^>]*data-test=""[^""]*milestone-date[^""]*""[^>]*>(.*?)<\/span>";

                        MatchCollection etaMatches = Regex.Matches(match.Value, etaPattern, RegexOptions.Singleline);
                        if (etaMatches.Count > 0)
                        {
                            etaValue = etaMatches[0].Groups[1].Value;
                        }
                    }
                }
                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("未找到正确的匹配对象");

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                string format = "dd MMM yyyy HH:mm";
                DateTime date;

                // 尝试解析输入字符串
                if (DateTime.TryParseExact(inputDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
                {
                    // 输出转换后的日期
                    return date.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }




    }
}
